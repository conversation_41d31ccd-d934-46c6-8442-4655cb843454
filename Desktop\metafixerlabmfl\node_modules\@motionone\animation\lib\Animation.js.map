{"version": 3, "file": "Animation.js", "sourceRoot": "", "sources": ["../src/Animation.ts"], "names": [], "mappings": "AAKA,OAAO,EACL,iBAAiB,EACjB,YAAY,EACZ,QAAQ,EACR,UAAU,EACV,WAAW,IAAI,iBAAiB,GACjC,MAAM,kBAAkB,CAAA;AACzB,OAAO,EAAE,iBAAiB,EAAE,MAAM,gBAAgB,CAAA;AAElD,MAAM,OAAO,SAAS;IA6BpB,YACE,MAA2B,EAC3B,YAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,EAC5B,EACE,MAAM,EACN,QAAQ,EAAE,eAAe,GAAG,QAAQ,CAAC,QAAQ,EAC7C,KAAK,GAAG,QAAQ,CAAC,KAAK,EACtB,QAAQ,GAAG,QAAQ,CAAC,QAAQ,EAC5B,MAAM,GAAG,QAAQ,CAAC,MAAM,EACxB,MAAM,EACN,SAAS,GAAG,QAAQ,EACpB,QAAQ,GAAG,IAAI,MACK,EAAE;QApC1B,cAAS,GAAkB,IAAI,CAAA;QAIvB,SAAI,GAAG,CAAC,CAAA;QAIR,MAAC,GAAG,CAAC,CAAA;QAEL,oBAAe,GAAkB,IAAI,CAAA;QAIrC,WAAM,GAAmB,UAAU,CAAA;QAEnC,aAAQ,GAAW,CAAC,CAAA;QAEpB,kBAAa,GAAW,CAAC,CAAA;QAEzB,WAAM,GAAW,CAAC,CAAA;QAE1B,cAAS,GAAuB,MAAM,CAAA;QA6HtC,aAAQ,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;YACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACtB,CAAC,CAAC,CAAA;QAhHA,MAAM,GAAG,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAA;QAElC,IAAI,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAA;YAChD,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;YACtB,SAAS,GAAI,MAAM,CAAC,SAAsB,IAAI,SAAS,CAAA;YACvD,eAAe,GAAG,MAAM,CAAC,QAAQ,IAAI,eAAe,CAAA;QACtD,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QAEpB,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QAC3E,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAA;QAEpC,MAAM,WAAW,GAAG,iBAAiB,CACnC,SAAS,EACT,MAAM,EACN,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,UAAU,CAClE,CAAA;QAED,IAAI,CAAC,IAAI,GAAG,CAAC,SAAiB,EAAE,EAAE;;YAChC,iDAAiD;YACjD,KAAK,GAAG,KAAe,CAAA;YAEvB,IAAI,CAAC,GAAG,CAAC,CAAA;YACT,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBACjC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAA;YACpB,CAAC;iBAAM,CAAC;gBACN,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAU,CAAC,GAAG,IAAI,CAAC,IAAI,CAAA;YAC/C,CAAC;YAED,IAAI,CAAC,CAAC,GAAG,CAAC,CAAA;YAEV,qBAAqB;YACrB,CAAC,IAAI,IAAI,CAAA;YAET,kBAAkB;YAClB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC,CAAA;YAE1B;;;eAGG;YACH,IAAI,IAAI,CAAC,SAAS,KAAK,UAAU,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBAClE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAA;YACxB,CAAC;YAED;;;;eAIG;YACH,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAA;YAElC,kCAAkC;YAElC;;;eAGG;YACH,IAAI,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YAE3C;;;eAGG;YACH,IAAI,iBAAiB,GAAG,QAAQ,GAAG,GAAG,CAAA;YAEtC,IAAI,CAAC,iBAAiB,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC;gBACxC,iBAAiB,GAAG,CAAC,CAAA;YACvB,CAAC;YAED;;;eAGG;YACH,iBAAiB,KAAK,CAAC,IAAI,gBAAgB,EAAE,CAAA;YAE7C;;eAEG;YACH,MAAM,cAAc,GAAG,gBAAgB,GAAG,CAAC,CAAA;YAC3C,IACE,SAAS,KAAK,SAAS;gBACvB,CAAC,SAAS,KAAK,WAAW,IAAI,cAAc,CAAC;gBAC7C,CAAC,SAAS,KAAK,mBAAmB,IAAI,CAAC,cAAc,CAAC,EACtD,CAAC;gBACD,iBAAiB,GAAG,CAAC,GAAG,iBAAiB,CAAA;YAC3C,CAAC;YAED,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAA;YACtE,MAAM,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1C,MAAM,CAAC,MAAM,CAAC,CAAA;YAEd,MAAM,mBAAmB,GACvB,IAAI,CAAC,SAAS,KAAK,SAAS;gBAC5B,CAAC,IAAI,CAAC,SAAS,KAAK,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,CAAA;YAEvE,IAAI,mBAAmB,EAAE,CAAC;gBACxB,IAAI,CAAC,SAAS,GAAG,UAAU,CAAA;gBAC3B,MAAA,IAAI,CAAC,OAAO,qDAAG,MAAM,CAAC,CAAA;YACxB,CAAC;iBAAM,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM,EAAE,CAAC;gBACrC,IAAI,CAAC,cAAc,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACxD,CAAC;QACH,CAAC,CAAA;QAED,IAAI,QAAQ;YAAE,IAAI,CAAC,IAAI,EAAE,CAAA;IAC3B,CAAC;IAOD,IAAI;QACF,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,CAAA;QAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAE1B,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS,CAAA;QACvC,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,GAAG,GAAG,CAAA;QACtB,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAA;QACrC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,cAAc,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACxD,CAAC;IAED,KAAK;QACH,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;QACzB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAA;IACzB,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,SAAS,GAAG,UAAU,CAAA;QAC3B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACd,CAAC;IAED,IAAI;;QACF,IAAI,CAAC,SAAS,GAAG,MAAM,CAAA;QAEvB,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACtC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAC3C,CAAC;QAED,MAAA,IAAI,CAAC,MAAM,qDAAG,KAAK,CAAC,CAAA;IACtB,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,IAAI,EAAE,CAAA;QACX,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAgB,CAAC,CAAA;IAClC,CAAC;IAED,OAAO;QACL,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAA;IACjB,CAAC;IAED,YAAY,KAAI,CAAC;IAET,cAAc,CAAC,QAAgB;QACrC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,aAAa,GAAG,QAAQ,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IACnD,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,CAAC,CAAA;IACf,CAAC;IAED,IAAI,WAAW,CAAC,CAAS;QACvB,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACpD,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;QACpB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAA;QACpD,CAAC;IACH,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,IAAI,CAAA;IAClB,CAAC;IAED,IAAI,YAAY,CAAC,IAAI;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;CACF"}