// ===============================================
// SYSTÈME DE ROTATIONS AVANCÉ - PlayMFL Manager
// VERSION CORRIGÉE AVEC FIX DES INDICES DE POSITIONS
// ===============================================

const RotationSystem = {
    
    // ===============================================
    // ROTATIONS SPÉCIFIQUES
    // ===============================================
    
    async rotateTeamFatigue(teamIndex) {
        return await this.rotateSpecificTeam(teamIndex, 'fatigue');
    },

    async rotateTeamCards(teamIndex) {
        return await this.rotateSpecificTeam(teamIndex, 'cards');
    },

    async rotateTeamAll(teamIndex) {
        return await this.smartRotateWithReorganization(teamIndex);
    },

    async rotateSpecificTeam(teamIndex, type) {
        const team = TeamManager.getTeam(teamIndex);
        const settings = StorageManager.loadSettings();
        const threshold = settings.fatigueThreshold;
        const maxEnergy = CONFIG.MAX_ENERGY;
        const minEnergyRequired = (threshold / 100) * maxEnergy;
        
        let rotationCount = 0;
        
        try {
            const clubPlayers = TeamManager.getClubPlayers(team.clubId);
            
            if (team.formation && team.formation.positions) {
                const newPositions = [...team.formation.positions];
                const formationType = team.formation.type;
                const expectedPositions = getFormationMapping(formationType);
                let needsUpdate = false;
                
                for (let i = 0; i < newPositions.length; i++) {
                    const position = newPositions[i];
                    const currentPlayer = clubPlayers.find(p => p.id === position.playerId);
                    
                    // CORRECTION : Utiliser l'index de la position pour déterminer le poste attendu
                    const positionIndex = position.index !== undefined ? position.index : i;
                    const targetPosition = expectedPositions[positionIndex] || 'CM';
                    
                    let shouldReplace = false;
                    
                    if (currentPlayer) {
                        if (type === 'fatigue' || type === 'all') {
                            shouldReplace = currentPlayer.energy < minEnergyRequired;
                        }
                        if ((type === 'cards' || type === 'all') && !shouldReplace) {
                            shouldReplace = PlayerSystem.isPlayerSuspended(currentPlayer);
                        }
                        
                        if (shouldReplace) {
                            const replacement = PlayerSystem.findReplacementAdvanced(
                                currentPlayer, 
                                clubPlayers, 
                                targetPosition,
                                type.includes('fatigue') ? minEnergyRequired : 0, 
                                team.formation.positions, 
                                type.includes('cards')
                            );
                            
                            if (replacement) {
                                // CORRECTION : Préserver l'index de la position
                                newPositions[i] = { 
                                    index: positionIndex,
                                    playerId: replacement.id 
                                };
                                needsUpdate = true;
                                rotationCount++;
                                
                                console.log(`🔄 ${team.squadName}: Remplacé ${currentPlayer.metadata?.firstName} par ${replacement.metadata?.firstName} en ${targetPosition} (index: ${positionIndex})`);
                            }
                        }
                    }
                }
                
                if (needsUpdate) {
                    const updatedFormation = await ApiManager.updateFormationPositions(team, newPositions);
                    if (updatedFormation) {
                        team.formation = updatedFormation;
                        UIManager.updateTeamCard(teamIndex);
                    }
                }
            }
        } catch (error) {
            console.error(`❌ Erreur rotation ${team.squadName}:`, error);
            throw error;
        }
        
        return {
            rotationCount,
            type,
            teamName: team.squadName
        };
    },

    // ===============================================
    // OPTIMISATION COMPLÈTE
    // ===============================================
    
    async optimizeTeamFormation(teamIndex) {
        const team = TeamManager.getTeam(teamIndex);
        if (!team.formation) return null;
        
        console.log(`🧠 Optimisation de ${team.squadName}...`);
        
        const clubPlayers = TeamManager.getClubPlayers(team.clubId);
        const optimizedPositions = PlayerSystem.optimizeFormation(team, clubPlayers);
        
        if (optimizedPositions && optimizedPositions.length > 0) {
            const updatedFormation = await ApiManager.updateFormationPositions(team, optimizedPositions);
            if (updatedFormation) {
                team.formation = updatedFormation;
                UIManager.updateTeamCard(teamIndex);
                
                const analysis = PlayerSystem.analyzeTeamStrength(team, clubPlayers);
                return {
                    success: true,
                    averageRating: analysis.averageRating,
                    teamName: team.squadName
                };
            }
        }
        
        return {
            success: false,
            message: 'Impossible d\'optimiser - Pas assez de joueurs',
            teamName: team.squadName
        };
    },

    // ===============================================
    // ROTATIONS AUTOMATIQUES AVANCÉES
    // ===============================================
    
    async autoRotateByPosition(teamIndex, targetPositions = []) {
        const team = TeamManager.getTeam(teamIndex);
        const clubPlayers = TeamManager.getClubPlayers(team.clubId);
        const formationType = team.formation.type;
        const expectedPositions = getFormationMapping(formationType);
        
        let rotationCount = 0;
        const newPositions = [...team.formation.positions];
        
        for (let i = 0; i < newPositions.length; i++) {
            const position = newPositions[i];
            
            // CORRECTION : Utiliser l'index de la position
            const positionIndex = position.index !== undefined ? position.index : i;
            const expectedPosition = expectedPositions[positionIndex];
            
            if (targetPositions.length === 0 || targetPositions.includes(expectedPosition)) {
                const currentPlayer = clubPlayers.find(p => p.id === position.playerId);
                
                if (currentPlayer) {
                    const currentRating = PlayerSystem.calculatePositionRating(currentPlayer, expectedPosition);
                    
                    // Chercher un meilleur joueur pour cette position
                    const betterReplacement = PlayerSystem.findReplacementAdvanced(
                        currentPlayer,
                        clubPlayers,
                        expectedPosition,
                        0,
                        team.formation.positions,
                        false
                    );
                    
                    if (betterReplacement) {
                        const newRating = PlayerSystem.calculatePositionRating(betterReplacement, expectedPosition);
                        
                        // Remplacer seulement si c'est significativement meilleur (+10 points minimum)
                        if (newRating > currentRating + 10) {
                            newPositions[i] = { 
                                index: positionIndex,
                                playerId: betterReplacement.id 
                            };
                            rotationCount++;
                            
                            console.log(`📈 ${team.squadName}: Amélioré ${expectedPosition} - ${currentPlayer.metadata?.firstName} (${currentRating}) → ${betterReplacement.metadata?.firstName} (${newRating})`);
                        }
                    }
                }
            }
        }
        
        if (rotationCount > 0) {
            const updatedFormation = await ApiManager.updateFormationPositions(team, newPositions);
            if (updatedFormation) {
                team.formation = updatedFormation;
                UIManager.updateTeamCard(teamIndex);
            }
        }
        
        return {
            rotationCount,
            teamName: team.squadName,
            type: 'optimization'
        };
    },

    // ===============================================
    // ROTATIONS EN MASSE
    // ===============================================
    
    async rotateAllTeams(rotationType = 'fatigue') {
        const teams = TeamManager.getAllTeams();
        const results = [];
        
        console.log(`🚀 Rotation en masse: ${rotationType} sur ${teams.length} équipe(s)`);
        
        for (let i = 0; i < teams.length; i++) {
            try {
                const result = await this.rotateSpecificTeam(i, rotationType);
                results.push(result);
            } catch (error) {
                console.error(`❌ Erreur rotation équipe ${i}:`, error);
                results.push({
                    rotationCount: 0,
                    type: rotationType,
                    teamName: teams[i].squadName,
                    error: error.message
                });
            }
        }
        
        const totalRotations = results.reduce((sum, result) => sum + result.rotationCount, 0);
        const successfulTeams = results.filter(r => r.rotationCount > 0).length;
        
        return {
            totalRotations,
            successfulTeams,
            totalTeams: teams.length,
            results
        };
    },

    async optimizeAllTeams() {
        const teams = TeamManager.getAllTeams();
        const results = [];
        
        console.log(`🧠 Optimisation en masse de ${teams.length} équipe(s)`);
        
        for (let i = 0; i < teams.length; i++) {
            try {
                const result = await this.optimizeTeamFormation(i);
                results.push(result);
            } catch (error) {
                console.error(`❌ Erreur optimisation équipe ${i}:`, error);
                results.push({
                    success: false,
                    teamName: teams[i].squadName,
                    error: error.message
                });
            }
        }
        
        const successfulTeams = results.filter(r => r.success).length;
        const avgRating = results
            .filter(r => r.success && r.averageRating)
            .reduce((sum, r, _, arr) => sum + r.averageRating / arr.length, 0);
        
        return {
            successfulTeams,
            totalTeams: teams.length,
            averageRating: Math.round(avgRating),
            results
        };
    },

    // ===============================================
    // ROTATION INTELLIGENTE AVEC RÉORGANISATION
    // ===============================================

    async smartRotateWithReorganization(teamIndex) {
        const team = TeamManager.getTeam(teamIndex);
        const settings = StorageManager.loadSettings();
        const threshold = settings.fatigueThreshold;
        const maxEnergy = CONFIG.MAX_ENERGY;
        const minEnergyRequired = (threshold / 100) * maxEnergy;

        if (!team.formation || !team.formation.positions) {
            return { rotationCount: 0, type: 'smart', teamName: team.squadName };
        }

        const clubPlayers = TeamManager.getClubPlayers(team.clubId);
        const formationType = team.formation.type;
        const expectedPositions = getFormationMapping(formationType);

        console.log(`🧠 Rotation intelligente avec réorganisation pour ${team.squadName}`);

        // 1. Identifier les joueurs problématiques
        const problematicPlayers = [];
        team.formation.positions.forEach((position, index) => {
            const player = clubPlayers.find(p => p.id === position.playerId);
            const positionIndex = position.index !== undefined ? position.index : index;
            const targetPosition = expectedPositions[positionIndex] || 'CM';

            if (player) {
                const isFatigued = player.energy < minEnergyRequired;
                const isSuspended = PlayerSystem.isPlayerSuspended(player);
                const familiarityRating = PlayerSystem.calculatePositionRating(player, targetPosition);

                if (isFatigued || isSuspended || familiarityRating < 50) {
                    problematicPlayers.push({
                        index,
                        positionIndex,
                        player,
                        targetPosition,
                        isFatigued,
                        isSuspended,
                        familiarityRating,
                        priority: isSuspended ? 3 : (isFatigued ? 2 : 1),
                        needsReplacement: isFatigued || isSuspended // Nouveau flag
                    });
                }
            }
        });

        // 2. Analyser les possibilités de réorganisation
        const reorganizationPlan = this.createReorganizationPlan(
            team,
            clubPlayers,
            expectedPositions,
            problematicPlayers,
            minEnergyRequired
        );

        // 3. Appliquer le plan de réorganisation
        let totalChanges = 0;
        let allChanges = [];

        if (reorganizationPlan.changes.length > 0) {
            const newPositions = this.applyReorganizationPlan(team.formation.positions, reorganizationPlan);

            const updatedFormation = await ApiManager.updateFormationPositions(team, newPositions);
            if (updatedFormation) {
                team.formation = updatedFormation;
                totalChanges += reorganizationPlan.changes.length;
                allChanges.push(...reorganizationPlan.changes);

                console.log(`✅ Remplacements terminés: ${reorganizationPlan.changes.length} changement(s)`);
                reorganizationPlan.changes.forEach(change => {
                    console.log(`   ${change.description}`);
                });
            }
        }

        // 4. NOUVEAU : Appliquer les échanges optimaux après les remplacements
        console.log(`🎯 Recherche d'échanges optimaux...`);
        const optimizationResult = await this.applyOptimalSwaps(team, clubPlayers, expectedPositions);

        if (optimizationResult.swapCount > 0) {
            totalChanges += optimizationResult.swapCount;
            allChanges.push(...optimizationResult.changes);

            console.log(`✅ Échanges optimaux terminés: ${optimizationResult.swapCount} échange(s)`);
            console.log(`📈 Gain total estimé: +${Math.round(optimizationResult.totalImprovement)} points`);
        }

        // 5. VÉRIFICATION POST-CHANGEMENT : Détecter les joueurs mal positionnés
        console.log(`🔍 Vérification post-changement...`);
        const postVerificationResult = await this.verifyAndFixMisplacements(team, clubPlayers, expectedPositions);

        if (postVerificationResult.fixCount > 0) {
            totalChanges += postVerificationResult.fixCount;
            allChanges.push(...postVerificationResult.changes);

            console.log(`✅ Corrections post-vérification: ${postVerificationResult.fixCount} correction(s)`);
        }

        UIManager.updateTeamCard(teamIndex);

        if (totalChanges > 0) {
            return {
                rotationCount: totalChanges,
                type: 'smart-reorganization-with-optimization',
                teamName: team.squadName,
                changes: allChanges,
                swapCount: optimizationResult.swapCount || 0,
                totalImprovement: optimizationResult.totalImprovement || 0
            };
        }

        console.log(`ℹ️ Aucune amélioration nécessaire pour ${team.squadName}`);
        return { rotationCount: 0, type: 'smart-reorganization', teamName: team.squadName };
    },

    createReorganizationPlan(team, clubPlayers, expectedPositions, problematicPlayers, minEnergyRequired) {
        const changes = [];
        const currentPositions = [...team.formation.positions];
        const availablePlayers = clubPlayers.filter(p =>
            !currentPositions.some(pos => pos.playerId === p.id) &&
            p.energy >= minEnergyRequired &&
            !PlayerSystem.isPlayerSuspended(p)
        );

        // Trier les problèmes par priorité (suspensions > fatigue > mauvaise position)
        problematicPlayers.sort((a, b) => b.priority - a.priority);

        for (const problem of problematicPlayers) {
            let allSolutions = [];

            // Pour les remplacements (cartons/fatigue), prioriser les chaînes
            if (problem.needsReplacement) {
                console.log(`🔗 Recherche de chaînes pour remplacer ${problem.player.metadata?.firstName?.charAt(0) || '?'}. ${problem.player.metadata?.lastName || 'Inconnu'} (${problem.targetPosition})`);

                // Chercher les chaînes de remplacement
                const replacementChains = this.findReplacementChains(
                    problem,
                    currentPositions,
                    availablePlayers,
                    expectedPositions,
                    minEnergyRequired,
                    clubPlayers
                );

                allSolutions.push(...replacementChains);
            }

            // Chercher les solutions en chaîne classiques
            const chainSolutions = this.findChainSolutions(
                problem,
                currentPositions,
                availablePlayers,
                expectedPositions,
                minEnergyRequired,
                clubPlayers
            );

            // Puis les solutions simples
            const simpleSolutions = this.findSolutionsForPosition(
                problem,
                currentPositions,
                availablePlayers,
                expectedPositions,
                minEnergyRequired,
                clubPlayers
            );

            // Combiner et trier toutes les solutions
            allSolutions.push(...chainSolutions, ...simpleSolutions);
            allSolutions.sort((a, b) => b.quality - a.quality);

            if (allSolutions.length > 0) {
                const bestSolution = allSolutions[0];
                changes.push(bestSolution);

                console.log(`🔧 Application de: ${bestSolution.description}`);

                // Appliquer la solution selon son type
                if (bestSolution.type === 'replacement') {
                    currentPositions[problem.index] = {
                        index: problem.positionIndex,
                        playerId: bestSolution.newPlayer.id
                    };
                    // Retirer le joueur des disponibles
                    const playerIndex = availablePlayers.findIndex(p => p.id === bestSolution.newPlayer.id);
                    if (playerIndex >= 0) availablePlayers.splice(playerIndex, 1);

                } else if (bestSolution.type === 'swap') {
                    // Échanger les joueurs entre les deux positions
                    const swapIndex = bestSolution.swapIndex;
                    const tempPlayerId = currentPositions[problem.index].playerId;

                    currentPositions[problem.index] = {
                        index: currentPositions[problem.index].index,
                        playerId: currentPositions[swapIndex].playerId
                    };

                    currentPositions[swapIndex] = {
                        index: currentPositions[swapIndex].index,
                        playerId: tempPlayerId
                    };

                } else if (bestSolution.type === 'chain' || bestSolution.type === 'replacement_chain') {
                    // Appliquer la chaîne de déplacements
                    bestSolution.moves.forEach(move => {
                        currentPositions[move.toIndex] = {
                            index: currentPositions[move.toIndex].index,
                            playerId: move.playerId
                        };
                    });

                    // Retirer les nouveaux joueurs des disponibles
                    if (bestSolution.newPlayers) {
                        bestSolution.newPlayers.forEach(newPlayer => {
                            const playerIndex = availablePlayers.findIndex(p => p.id === newPlayer.id);
                            if (playerIndex >= 0) availablePlayers.splice(playerIndex, 1);
                        });
                    }
                }
            }
        }

        return { changes, finalPositions: currentPositions };
    },

    findReplacementChains(problem, currentPositions, availablePlayers, expectedPositions, minEnergyRequired, clubPlayers) {
        const chains = [];

        // Pour chaque joueur actuellement dans l'équipe (non problématique)
        for (let i = 0; i < currentPositions.length; i++) {
            if (i === problem.index) continue;

            const candidatePosition = currentPositions[i];
            const candidatePlayer = clubPlayers.find(p => p.id === candidatePosition.playerId);

            if (!candidatePlayer ||
                candidatePlayer.energy < minEnergyRequired ||
                PlayerSystem.isPlayerSuspended(candidatePlayer)) {
                continue;
            }

            // Vérifier si ce joueur peut jouer à la position du problème
            const candidateRatingAtProblemPos = PlayerSystem.calculatePositionRating(candidatePlayer, problem.targetPosition);

            // Si le candidat peut bien jouer à la position problématique
            if (candidateRatingAtProblemPos > 70) { // Seuil plus élevé pour les chaînes de remplacement

                // Chercher un remplaçant du banc pour la position du candidat
                const candidateTargetPosition = expectedPositions[candidatePosition.index] || 'CM';
                const replacements = availablePlayers
                    .filter(player =>
                        PlayerSystem.canPlayPosition(player, candidateTargetPosition) &&
                        player.energy >= minEnergyRequired &&
                        !PlayerSystem.isPlayerSuspended(player)
                    )
                    .map(player => ({
                        player,
                        rating: PlayerSystem.calculatePositionRating(player, candidateTargetPosition)
                    }))
                    .sort((a, b) => b.rating - a.rating);

                if (replacements.length > 0) {
                    const bestReplacement = replacements[0];

                    // Calculer la qualité de la chaîne
                    const chainQuality = candidateRatingAtProblemPos + bestReplacement.rating;

                    const candidateName = `${candidatePlayer.metadata?.firstName?.charAt(0) || '?'}. ${candidatePlayer.metadata?.lastName || 'Inconnu'}`;
                    const replacementName = `${bestReplacement.player.metadata?.firstName?.charAt(0) || '?'}. ${bestReplacement.player.metadata?.lastName || 'Inconnu'}`;

                    chains.push({
                        type: 'replacement_chain',
                        quality: chainQuality,
                        moves: [
                            { playerId: candidatePlayer.id, toIndex: problem.index },
                            { playerId: bestReplacement.player.id, toIndex: i }
                        ],
                        newPlayers: [bestReplacement.player],
                        description: `🔗 Chaîne de remplacement: ${candidateName} → ${problem.targetPosition}, ${replacementName} → ${candidateTargetPosition} (Qualité: ${Math.round(chainQuality)})`
                    });
                }
            }
        }

        return chains.sort((a, b) => b.quality - a.quality);
    },

    findChainSolutions(problem, currentPositions, availablePlayers, expectedPositions, minEnergyRequired, clubPlayers) {
        const chainSolutions = [];

        // Pour chaque joueur actuellement dans l'équipe
        for (let i = 0; i < currentPositions.length; i++) {
            if (i === problem.index) continue;

            const candidatePosition = currentPositions[i];
            const candidatePlayer = clubPlayers.find(p => p.id === candidatePosition.playerId);

            if (!candidatePlayer ||
                candidatePlayer.energy < minEnergyRequired ||
                PlayerSystem.isPlayerSuspended(candidatePlayer)) {
                continue;
            }

            // Vérifier si ce joueur peut mieux jouer à la position problématique
            const candidateRatingAtProblemPos = PlayerSystem.calculatePositionRating(candidatePlayer, problem.targetPosition);
            const candidateCurrentRating = PlayerSystem.calculatePositionRating(candidatePlayer, expectedPositions[candidatePosition.index] || 'CM');

            // Si le candidat est meilleur à la position problématique
            if (candidateRatingAtProblemPos > problem.familiarityRating + 10) {

                // Chercher un remplaçant du banc pour la position du candidat
                const candidateTargetPosition = expectedPositions[candidatePosition.index] || 'CM';
                const replacements = availablePlayers
                    .filter(player =>
                        PlayerSystem.canPlayPosition(player, candidateTargetPosition) &&
                        player.energy >= minEnergyRequired &&
                        !PlayerSystem.isPlayerSuspended(player)
                    )
                    .map(player => ({
                        player,
                        rating: PlayerSystem.calculatePositionRating(player, candidateTargetPosition)
                    }))
                    .sort((a, b) => b.rating - a.rating);

                if (replacements.length > 0) {
                    const bestReplacement = replacements[0];

                    // Calculer l'amélioration totale de la chaîne
                    const currentTotal = problem.familiarityRating + candidateCurrentRating;
                    const chainTotal = candidateRatingAtProblemPos + bestReplacement.rating;
                    const improvement = chainTotal - currentTotal;

                    if (improvement > 5) {
                        chainSolutions.push({
                            type: 'chain',
                            quality: improvement,
                            moves: [
                                { playerId: candidatePlayer.id, toIndex: problem.index },
                                { playerId: bestReplacement.player.id, toIndex: i }
                            ],
                            newPlayers: [bestReplacement.player],
                            description: `🔗 Chaîne: ${candidatePlayer.metadata?.firstName?.charAt(0) || '?'}. ${candidatePlayer.metadata?.lastName || 'Inconnu'} → ${problem.targetPosition}, ${bestReplacement.player.metadata?.firstName?.charAt(0) || '?'}. ${bestReplacement.player.metadata?.lastName || 'Inconnu'} → ${candidateTargetPosition} = +${Math.round(improvement)} points`
                        });
                    }
                }
            }
        }

        return chainSolutions.sort((a, b) => b.quality - a.quality);
    },

    findSolutionsForPosition(problem, currentPositions, availablePlayers, expectedPositions, minEnergyRequired, clubPlayers) {
        const solutions = [];

        // Solution 1: Remplacement direct par un joueur disponible
        const directReplacements = availablePlayers
            .filter(player =>
                PlayerSystem.canPlayPosition(player, problem.targetPosition) &&
                player.energy >= minEnergyRequired
            )
            .map(player => ({
                type: 'replacement',
                quality: PlayerSystem.calculatePositionRating(player, problem.targetPosition),
                newPlayer: player,
                description: `🔄 Remplacer ${problem.player.metadata?.firstName?.charAt(0) || '?'}. ${problem.player.metadata?.lastName || 'Inconnu'} par ${player.metadata?.firstName?.charAt(0) || '?'}. ${player.metadata?.lastName || 'Inconnu'} en ${problem.targetPosition}`
            }))
            .sort((a, b) => b.quality - a.quality);

        solutions.push(...directReplacements.slice(0, 3)); // Top 3 remplacements

        // Solution 2: Échange avec un autre joueur de l'équipe
        const clubPlayersMap = new Map();
        clubPlayers.forEach(p => clubPlayersMap.set(p.id, p));

        for (let i = 0; i < currentPositions.length; i++) {
            if (i === problem.index) continue;

            const otherPosition = currentPositions[i];
            const otherPlayer = clubPlayersMap.get(otherPosition.playerId);
            const otherTargetPosition = expectedPositions[otherPosition.index || i] || 'CM';

            if (otherPlayer &&
                otherPlayer.energy >= minEnergyRequired &&
                !PlayerSystem.isPlayerSuspended(otherPlayer)) {

                // Vérifier si l'échange améliore les deux positions
                const problemPlayerInOtherPos = PlayerSystem.calculatePositionRating(problem.player, otherTargetPosition);
                const otherPlayerInProblemPos = PlayerSystem.calculatePositionRating(otherPlayer, problem.targetPosition);

                const currentRatingOther = PlayerSystem.calculatePositionRating(otherPlayer, otherTargetPosition);
                const currentTotal = problem.familiarityRating + currentRatingOther;
                const swapTotal = problemPlayerInOtherPos + otherPlayerInProblemPos;

                if (swapTotal > currentTotal + 5) { // Amélioration moins stricte
                    solutions.push({
                        type: 'swap',
                        quality: swapTotal - currentTotal,
                        swapIndex: i,
                        otherPlayer: otherPlayer,
                        description: `🔄 Échanger ${problem.player.metadata?.firstName} (${problem.targetPosition}) avec ${otherPlayer.metadata?.firstName} (${otherTargetPosition}) - Gain: +${Math.round(swapTotal - currentTotal)}`
                    });
                }
            }
        }

        return solutions.sort((a, b) => b.quality - a.quality);
    },

    applyReorganizationPlan(originalPositions, plan) {
        // Utiliser les positions finales calculées dans la planification
        if (plan.finalPositions && plan.finalPositions.length > 0) {
            return plan.finalPositions;
        }

        // Si pas de positions finales, reconstruire à partir des changements
        const newPositions = [...originalPositions];

        plan.changes.forEach(change => {
            if (change.type === 'replacement' && change.newPlayer) {
                // Trouver l'index du changement (approximatif)
                const problemIndex = plan.changes.indexOf(change);
                if (problemIndex >= 0 && problemIndex < newPositions.length) {
                    newPositions[problemIndex] = {
                        index: originalPositions[problemIndex].index,
                        playerId: change.newPlayer.id
                    };
                }
            } else if ((change.type === 'chain' || change.type === 'replacement_chain') && change.moves) {
                // Appliquer tous les mouvements de la chaîne
                change.moves.forEach(move => {
                    newPositions[move.toIndex] = {
                        index: newPositions[move.toIndex].index,
                        playerId: move.playerId
                    };
                });
            }
        });

        return newPositions;
    },

    async applyOptimalSwaps(team, clubPlayers, expectedPositions) {
        let totalImprovement = 0;
        let swapCount = 0;
        let changes = [];

        // Appliquer les échanges un par un jusqu'à ce qu'il n'y en ait plus
        for (let i = 0; i < 3; i++) { // Maximum 3 échanges pour éviter les boucles infinies
            const improvements = this.findOptimalSwapsForTeam(team, clubPlayers, expectedPositions);

            if (improvements.length === 0 || improvements[0].improvement < 5) {
                break; // Plus d'amélioration significative
            }

            const bestSwap = improvements[0];
            console.log(`🔄 Échange ${i + 1}: ${bestSwap.description}`);

            // Appliquer l'échange
            const newPositions = [...team.formation.positions];
            const tempPlayerId = newPositions[bestSwap.pos1].playerId;
            newPositions[bestSwap.pos1] = {
                index: newPositions[bestSwap.pos1].index,
                playerId: newPositions[bestSwap.pos2].playerId
            };
            newPositions[bestSwap.pos2] = {
                index: newPositions[bestSwap.pos2].index,
                playerId: tempPlayerId
            };

            try {
                const updatedFormation = await ApiManager.updateFormationPositions(team, newPositions);
                if (updatedFormation) {
                    team.formation = updatedFormation;
                    totalImprovement += bestSwap.improvement;
                    swapCount++;
                    changes.push({
                        type: 'swap',
                        description: bestSwap.description,
                        improvement: bestSwap.improvement
                    });

                    // Attendre un peu avant le prochain échange
                    await new Promise(resolve => setTimeout(resolve, 300));
                } else {
                    break;
                }
            } catch (error) {
                console.error('❌ Erreur lors de l\'échange:', error);
                break;
            }
        }

        return {
            swapCount,
            totalImprovement,
            changes
        };
    },

    findOptimalSwapsForTeam(team, clubPlayers, expectedPositions) {
        const improvements = [];

        // Analyser chaque position pour trouver des améliorations possibles
        for (let i = 0; i < team.formation.positions.length; i++) {
            const position = team.formation.positions[i];
            const player = clubPlayers.find(p => p.id === position.playerId);
            const positionIndex = position.index !== undefined ? position.index : i;
            const targetPosition = expectedPositions[positionIndex] || 'CM';

            if (!player) continue;

            const currentRating = PlayerSystem.calculatePositionRating(player, targetPosition);
            const familiarity = PlayerSystem.getPositionFamiliarity(player, targetPosition);

            // Ne chercher des échanges que pour les positions UNFAMILIAR ou SOMEWHAT_FAMILIAR
            if (familiarity === 'UNFAMILIAR' || familiarity === 'SOMEWHAT_FAMILIAR') {

                // Chercher des échanges possibles avec d'autres positions
                for (let j = 0; j < team.formation.positions.length; j++) {
                    if (i === j) continue;

                    const otherPosition = team.formation.positions[j];
                    const otherPlayer = clubPlayers.find(p => p.id === otherPosition.playerId);
                    const otherPositionIndex = otherPosition.index !== undefined ? otherPosition.index : j;
                    const otherTargetPosition = expectedPositions[otherPositionIndex] || 'CM';

                    if (!otherPlayer) continue;

                    // Calculer les ratings si on échange
                    const playerInOtherPos = PlayerSystem.calculatePositionRating(player, otherTargetPosition);
                    const otherPlayerInCurrentPos = PlayerSystem.calculatePositionRating(otherPlayer, targetPosition);
                    const otherPlayerCurrentRating = PlayerSystem.calculatePositionRating(otherPlayer, otherTargetPosition);

                    const currentTotal = currentRating + otherPlayerCurrentRating;
                    const swapTotal = playerInOtherPos + otherPlayerInCurrentPos;
                    const improvement = swapTotal - currentTotal;

                    if (improvement > 5) {
                        improvements.push({
                            pos1: i,
                            pos2: j,
                            player1: player,
                            player2: otherPlayer,
                            targetPos1: targetPosition,
                            targetPos2: otherTargetPosition,
                            currentTotal,
                            swapTotal,
                            improvement,
                            description: `Échanger ${player.metadata?.firstName?.charAt(0) || '?'}. ${player.metadata?.lastName || 'Inconnu'} (${targetPosition}) avec ${otherPlayer.metadata?.firstName?.charAt(0) || '?'}. ${otherPlayer.metadata?.lastName || 'Inconnu'} (${otherTargetPosition}) = +${Math.round(improvement)} points`
                        });
                    }
                }
            }
        }

        // Trier par amélioration décroissante
        return improvements.sort((a, b) => b.improvement - a.improvement);
    },

    async verifyAndFixMisplacements(team, clubPlayers, expectedPositions) {
        const misplacements = [];
        let fixCount = 0;
        let changes = [];

        // Détecter les joueurs gravement mal positionnés
        team.formation.positions.forEach((position, index) => {
            const player = clubPlayers.find(p => p.id === position.playerId);
            const positionIndex = position.index !== undefined ? position.index : index;
            const targetPosition = expectedPositions[positionIndex] || 'CM';

            if (player) {
                const familiarity = PlayerSystem.getPositionFamiliarity(player, targetPosition);
                const rating = PlayerSystem.calculatePositionRating(player, targetPosition);

                // Critères de mauvais positionnement
                const isTerribleMatch = familiarity === 'UNFAMILIAR' && rating < 40;
                const isPositionMismatch = this.isPositionTypeMismatch(player.metadata?.positions || [], targetPosition);

                if (isTerribleMatch || isPositionMismatch) {
                    misplacements.push({
                        index,
                        positionIndex,
                        player,
                        targetPosition,
                        familiarity,
                        rating,
                        reason: isPositionMismatch ? 'TYPE_MISMATCH' : 'TERRIBLE_RATING'
                    });
                }
            }
        });

        if (misplacements.length === 0) {
            console.log(`✅ Aucun mauvais positionnement détecté`);
            return { fixCount: 0, changes: [] };
        }

        console.log(`⚠️ ${misplacements.length} mauvais positionnement(s) détecté(s):`);
        misplacements.forEach(mp => {
            const playerName = `${mp.player.metadata?.firstName?.charAt(0) || '?'}. ${mp.player.metadata?.lastName || 'Inconnu'}`;
            console.log(`   ${playerName} [${mp.player.metadata?.positions?.join(',')}] en ${mp.targetPosition} (${mp.reason})`);
        });

        // Tenter de corriger les mauvais positionnements
        for (const misplacement of misplacements) {
            const correction = await this.findBestCorrectionForMisplacement(
                misplacement,
                team,
                clubPlayers,
                expectedPositions
            );

            if (correction) {
                console.log(`🔧 ${correction.description}`);

                // Appliquer la correction
                const newPositions = [...team.formation.positions];
                correction.moves.forEach(move => {
                    newPositions[move.toIndex] = {
                        index: newPositions[move.toIndex].index,
                        playerId: move.playerId
                    };
                });

                try {
                    const updatedFormation = await ApiManager.updateFormationPositions(team, newPositions);
                    if (updatedFormation) {
                        team.formation = updatedFormation;
                        fixCount++;
                        changes.push(correction);

                        // Attendre un peu avant la prochaine correction
                        await new Promise(resolve => setTimeout(resolve, 300));
                    }
                } catch (error) {
                    console.error('❌ Erreur lors de la correction:', error);
                }
            }
        }

        return { fixCount, changes };
    },

    isPositionTypeMismatch(playerPositions, targetPosition) {
        // Vérifier si un joueur est complètement dans le mauvais secteur
        const playerTypes = this.getPositionTypes(playerPositions);
        const targetType = this.getPositionType(targetPosition);

        // Cas critiques : milieu/attaquant en défense
        if (targetType === 'DEFENSE' && (playerTypes.includes('MIDFIELD') || playerTypes.includes('ATTACK'))) {
            return !playerPositions.some(pos => ['CB', 'LB', 'RB', 'LWB', 'RWB'].includes(pos));
        }

        return false;
    },

    getPositionTypes(positions) {
        const types = [];
        positions.forEach(pos => {
            const type = this.getPositionType(pos);
            if (!types.includes(type)) types.push(type);
        });
        return types;
    },

    getPositionType(position) {
        if (['GK', 'G'].includes(position)) return 'GOALKEEPER';
        if (['CB', 'LB', 'RB', 'LWB', 'RWB', 'DC', 'DLG', 'DLD'].includes(position)) return 'DEFENSE';
        if (['CDM', 'CM', 'LM', 'RM', 'CAM', 'MDC', 'MC', 'MG', 'MD', 'MOC'].includes(position)) return 'MIDFIELD';
        if (['ST', 'CF', 'LW', 'RW', 'BU', 'AT', 'AG', 'AD'].includes(position)) return 'ATTACK';
        return 'UNKNOWN';
    },

    async findBestCorrectionForMisplacement(misplacement, team, clubPlayers, expectedPositions) {
        // Chercher des échanges avec d'autres joueurs de l'équipe
        for (let i = 0; i < team.formation.positions.length; i++) {
            if (i === misplacement.index) continue;

            const otherPosition = team.formation.positions[i];
            const otherPlayer = clubPlayers.find(p => p.id === otherPosition.playerId);
            const otherTargetPosition = expectedPositions[otherPosition.index || i] || 'CM';

            if (!otherPlayer) continue;

            // Vérifier si l'échange améliore les deux positions
            const misplacedPlayerInOtherPos = PlayerSystem.calculatePositionRating(misplacement.player, otherTargetPosition);
            const otherPlayerInMisplacedPos = PlayerSystem.calculatePositionRating(otherPlayer, misplacement.targetPosition);

            const currentTotal = misplacement.rating + PlayerSystem.calculatePositionRating(otherPlayer, otherTargetPosition);
            const swapTotal = misplacedPlayerInOtherPos + otherPlayerInMisplacedPos;

            // Forcer la correction des mauvais positionnements (pas de seuil)
            if (swapTotal > currentTotal) {
                const playerName1 = `${misplacement.player.metadata?.firstName?.charAt(0) || '?'}. ${misplacement.player.metadata?.lastName || 'Inconnu'}`;
                const playerName2 = `${otherPlayer.metadata?.firstName?.charAt(0) || '?'}. ${otherPlayer.metadata?.lastName || 'Inconnu'}`;

                return {
                    type: 'correction_swap',
                    moves: [
                        { playerId: otherPlayer.id, toIndex: misplacement.index },
                        { playerId: misplacement.player.id, toIndex: i }
                    ],
                    description: `🔧 Correction: Échanger ${playerName1} (${misplacement.targetPosition}) avec ${playerName2} (${otherTargetPosition}) = +${Math.round(swapTotal - currentTotal)} points`
                };
            }
        }

        // Si aucun échange n'est possible, chercher un remplacement direct du banc
        const availablePlayers = clubPlayers.filter(p =>
            !team.formation.positions.some(pos => pos.playerId === p.id) &&
            p.energy >= 3000 && // Seuil minimal d'énergie
            !PlayerSystem.isPlayerSuspended(p)
        );

        const directReplacements = availablePlayers
            .filter(player => PlayerSystem.canPlayPosition(player, misplacement.targetPosition))
            .map(player => ({
                player,
                rating: PlayerSystem.calculatePositionRating(player, misplacement.targetPosition)
            }))
            .sort((a, b) => b.rating - a.rating);

        if (directReplacements.length > 0) {
            const bestReplacement = directReplacements[0];

            // Accepter tout remplacement qui améliore la situation
            if (bestReplacement.rating > misplacement.rating) {
                const playerName1 = `${misplacement.player.metadata?.firstName?.charAt(0) || '?'}. ${misplacement.player.metadata?.lastName || 'Inconnu'}`;
                const playerName2 = `${bestReplacement.player.metadata?.firstName?.charAt(0) || '?'}. ${bestReplacement.player.metadata?.lastName || 'Inconnu'}`;

                return {
                    type: 'correction_replacement',
                    moves: [
                        { playerId: bestReplacement.player.id, toIndex: misplacement.index }
                    ],
                    description: `🔧 Correction: Remplacer ${playerName1} par ${playerName2} en ${misplacement.targetPosition} = +${Math.round(bestReplacement.rating - misplacement.rating)} points`
                };
            }
        }

        return null;
    },

    // ===============================================
    // ROTATIONS INTELLIGENTES
    // ===============================================
    
    async smartRotation(teamIndex, options = {}) {
        const {
            prioritizeEnergy = true,
            avoidSuspensions = true,
            minRatingImprovement = 5,
            maxChanges = 3
        } = options;
        
        const team = TeamManager.getTeam(teamIndex);
        const clubPlayers = TeamManager.getClubPlayers(team.clubId);
        const settings = StorageManager.loadSettings();
        
        if (!team.formation || !team.formation.positions) return null;
        
        const formationType = team.formation.type;
        const expectedPositions = getFormationMapping(formationType);
        const newPositions = [...team.formation.positions];
        
        let changesMade = 0;
        const changes = [];
        
        // Analyser chaque position
        for (let i = 0; i < newPositions.length && changesMade < maxChanges; i++) {
            const position = newPositions[i];
            const currentPlayer = clubPlayers.find(p => p.id === position.playerId);
            
            // CORRECTION : Utiliser l'index de la position
            const positionIndex = position.index !== undefined ? position.index : i;
            const expectedPosition = expectedPositions[positionIndex] || 'CM';
            
            if (!currentPlayer) continue;
            
            const currentRating = PlayerSystem.calculatePositionRating(currentPlayer, expectedPosition);
            let shouldReplace = false;
            let reason = '';
            
            // Vérifications prioritaires
            if (avoidSuspensions && PlayerSystem.isPlayerSuspended(currentPlayer)) {
                shouldReplace = true;
                reason = 'suspension';
            } else if (prioritizeEnergy && currentPlayer.energy < (settings.fatigueThreshold / 100) * CONFIG.MAX_ENERGY) {
                shouldReplace = true;
                reason = 'fatigue';
            } else {
                // Chercher une amélioration de rating
                const betterPlayer = PlayerSystem.findReplacementAdvanced(
                    currentPlayer,
                    clubPlayers,
                    expectedPosition,
                    prioritizeEnergy ? (settings.fatigueThreshold / 100) * CONFIG.MAX_ENERGY : 0,
                    team.formation.positions,
                    avoidSuspensions
                );
                
                if (betterPlayer) {
                    const betterRating = PlayerSystem.calculatePositionRating(betterPlayer, expectedPosition);
                    if (betterRating > currentRating + minRatingImprovement) {
                        shouldReplace = true;
                        reason = 'improvement';
                    }
                }
            }
            
            if (shouldReplace) {
                const replacement = PlayerSystem.findReplacementAdvanced(
                    currentPlayer,
                    clubPlayers,
                    expectedPosition,
                    prioritizeEnergy ? (settings.fatigueThreshold / 100) * CONFIG.MAX_ENERGY : 0,
                    team.formation.positions,
                    avoidSuspensions
                );
                
                if (replacement) {
                    const newRating = PlayerSystem.calculatePositionRating(replacement, expectedPosition);
                    
                    newPositions[i] = { 
                        index: positionIndex,
                        playerId: replacement.id 
                    };
                    changesMade++;
                    
                    changes.push({
                        position: expectedPosition,
                        oldPlayer: {
                            name: `${currentPlayer.metadata?.firstName} ${currentPlayer.metadata?.lastName}`,
                            rating: currentRating
                        },
                        newPlayer: {
                            name: `${replacement.metadata?.firstName} ${replacement.metadata?.lastName}`,
                            rating: newRating
                        },
                        reason: reason,
                        improvement: newRating - currentRating
                    });
                }
            }
        }
        
        if (changesMade > 0) {
            const updatedFormation = await ApiManager.updateFormationPositions(team, newPositions);
            if (updatedFormation) {
                team.formation = updatedFormation;
                UIManager.updateTeamCard(teamIndex);
            }
        }
        
        return {
            changesMade,
            changes,
            teamName: team.squadName
        };
    },

    // ===============================================
    // UTILITAIRES
    // ===============================================
    
    getRotationSummary(results) {
        if (Array.isArray(results)) {
            // Résultats multiples (rotation en masse)
            const total = results.reduce((sum, r) => sum + r.rotationCount, 0);
            const successful = results.filter(r => r.rotationCount > 0).length;
            return `${total} changement(s) sur ${successful}/${results.length} équipe(s)`;
        } else {
            // Résultat unique
            return `${results.rotationCount} changement(s) dans ${results.teamName}`;
        }
    },

    formatRotationType(type) {
        const types = {
            'fatigue': 'joueurs fatigués',
            'cards': 'joueurs cartonnés',
            'all': 'fatigués/cartonnés',
            'optimization': 'optimisation'
        };
        return types[type] || type;
    }
};

console.log('✅ Système de rotations avancé chargé (version corrigée)');