{"version": 3, "file": "property.js", "sources": ["../../../src/decorators/property.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport {PropertyDeclaration, ReactiveElement} from '../reactive-element.js';\nimport {ClassElement} from './base.js';\n\nconst standardProperty = (\n  options: PropertyDeclaration,\n  element: ClassElement\n) => {\n  // When decorating an accessor, pass it through and add property metadata.\n  // Note, the `hasOwnProperty` check in `createProperty` ensures we don't\n  // stomp over the user's accessor.\n  if (\n    element.kind === 'method' &&\n    element.descriptor &&\n    !('value' in element.descriptor)\n  ) {\n    return {\n      ...element,\n      finisher(clazz: typeof ReactiveElement) {\n        clazz.createProperty(element.key, options);\n      },\n    };\n  } else {\n    // createProperty() takes care of defining the property, but we still\n    // must return some kind of descriptor, so return a descriptor for an\n    // unused prototype field. The finisher calls createProperty().\n    return {\n      kind: 'field',\n      key: Symbol(),\n      placement: 'own',\n      descriptor: {},\n      // store the original key so subsequent decorators have access to it.\n      originalKey: element.key,\n      // When @babel/plugin-proposal-decorators implements initializers,\n      // do this instead of the initializer below. See:\n      // https://github.com/babel/babel/issues/9260 extras: [\n      //   {\n      //     kind: 'initializer',\n      //     placement: 'own',\n      //     initializer: descriptor.initializer,\n      //   }\n      // ],\n      initializer(this: {[key: string]: unknown}) {\n        if (typeof element.initializer === 'function') {\n          this[element.key as string] = element.initializer.call(this);\n        }\n      },\n      finisher(clazz: typeof ReactiveElement) {\n        clazz.createProperty(element.key, options);\n      },\n    };\n  }\n};\n\nconst legacyProperty = (\n  options: PropertyDeclaration,\n  proto: Object,\n  name: PropertyKey\n) => {\n  (proto.constructor as typeof ReactiveElement).createProperty(name, options);\n};\n\n/**\n * A property decorator which creates a reactive property that reflects a\n * corresponding attribute value. When a decorated property is set\n * the element will update and render. A {@linkcode PropertyDeclaration} may\n * optionally be supplied to configure property features.\n *\n * This decorator should only be used for public fields. As public fields,\n * properties should be considered as primarily settable by element users,\n * either via attribute or the property itself.\n *\n * Generally, properties that are changed by the element should be private or\n * protected fields and should use the {@linkcode state} decorator.\n *\n * However, sometimes element code does need to set a public property. This\n * should typically only be done in response to user interaction, and an event\n * should be fired informing the user; for example, a checkbox sets its\n * `checked` property when clicked and fires a `changed` event. Mutating public\n * properties should typically not be done for non-primitive (object or array)\n * properties. In other cases when an element needs to manage state, a private\n * property decorated via the {@linkcode state} decorator should be used. When\n * needed, state properties can be initialized via public properties to\n * facilitate complex interactions.\n *\n * ```ts\n * class MyElement {\n *   @property({ type: Boolean })\n *   clicked = false;\n * }\n * ```\n * @category Decorator\n * @ExportDecoratedItems\n */\nexport function property(options?: PropertyDeclaration) {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return (protoOrDescriptor: Object | ClassElement, name?: PropertyKey): any =>\n    name !== undefined\n      ? legacyProperty(options!, protoOrDescriptor as Object, name)\n      : standardProperty(options!, protoOrDescriptor as ClassElement);\n}\n"], "names": [], "mappings": "AAAA;;;;AAIG;AAWH,MAAM,gBAAgB,GAAG,CACvB,OAA4B,EAC5B,OAAqB,KACnB;;;;AAIF,IAAA,IACE,OAAO,CAAC,IAAI,KAAK,QAAQ;AACzB,QAAA,OAAO,CAAC,UAAU;AAClB,QAAA,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,EAChC;QACA,OAAO;AACL,YAAA,GAAG,OAAO;AACV,YAAA,QAAQ,CAAC,KAA6B,EAAA;gBACpC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;aAC5C;SACF,CAAC;AACH,KAAA;AAAM,SAAA;;;;QAIL,OAAO;AACL,YAAA,IAAI,EAAE,OAAO;YACb,GAAG,EAAE,MAAM,EAAE;AACb,YAAA,SAAS,EAAE,KAAK;AAChB,YAAA,UAAU,EAAE,EAAE;;YAEd,WAAW,EAAE,OAAO,CAAC,GAAG;;;;;;;;;;YAUxB,WAAW,GAAA;AACT,gBAAA,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE;AAC7C,oBAAA,IAAI,CAAC,OAAO,CAAC,GAAa,CAAC,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9D,iBAAA;aACF;AACD,YAAA,QAAQ,CAAC,KAA6B,EAAA;gBACpC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;aAC5C;SACF,CAAC;AACH,KAAA;AACH,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CACrB,OAA4B,EAC5B,KAAa,EACb,IAAiB,KACf;IACD,KAAK,CAAC,WAAsC,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC9E,CAAC,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BG;AACG,SAAU,QAAQ,CAAC,OAA6B,EAAA;;IAEpD,OAAO,CAAC,iBAAwC,EAAE,IAAkB,KAClE,IAAI,KAAK,SAAS;UACd,cAAc,CAAC,OAAQ,EAAE,iBAA2B,EAAE,IAAI,CAAC;AAC7D,UAAE,gBAAgB,CAAC,OAAQ,EAAE,iBAAiC,CAAC,CAAC;AACtE;;;;"}