{"version": 3, "file": "handle-element.js", "sourceRoot": "", "sources": ["../../../src/gestures/resize/handle-element.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAA;AAG9D,MAAM,cAAc,GAAG,IAAI,OAAO,EAAwC,CAAA;AAE1E,IAAI,QAAoC,CAAA;AAExC,SAAS,cAAc,CACrB,MAAe,EACf,aAAiD;IAEjD,IAAI,aAAa,EAAE,CAAC;QAClB,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;QAClD,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,CAAA;IACjD,CAAC;SAAM,IAAI,MAAM,YAAY,UAAU,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;QAC/D,OAAQ,MAA6B,CAAC,OAAO,EAAE,CAAA;IACjD,CAAC;SAAM,CAAC;QACN,OAAO;YACL,KAAK,EAAG,MAAsB,CAAC,WAAW;YAC1C,MAAM,EAAG,MAAsB,CAAC,YAAY;SAC7C,CAAA;IACH,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CAAC,EACpB,MAAM,EACN,WAAW,EACX,aAAa,GACO;;IACpB,MAAA,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,0CAAE,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC9C,OAAO,CAAC;YACN,MAAM;YACN,WAAW,EAAE,WAAW;YACxB,IAAI,IAAI;gBACN,OAAO,cAAc,CAAC,MAAM,EAAE,aAAa,CAAC,CAAA;YAC9C,CAAC;SACF,CAAC,CAAA;IACJ,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,SAAS,SAAS,CAAC,OAA8B;IAC/C,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;AAC/B,CAAC;AAED,SAAS,oBAAoB;IAC3B,IAAI,OAAO,cAAc,KAAK,WAAW;QAAE,OAAM;IAEjD,QAAQ,GAAG,IAAI,cAAc,CAAC,SAAS,CAAC,CAAA;AAC1C,CAAC;AAED,MAAM,UAAU,aAAa,CAC3B,MAAyB,EACzB,OAA+B;IAE/B,IAAI,CAAC,QAAQ;QAAE,oBAAoB,EAAE,CAAA;IAErC,MAAM,QAAQ,GAAG,eAAe,CAAC,MAAM,CAAC,CAAA;IAExC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC3B,IAAI,eAAe,GAAG,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAEjD,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,eAAe,GAAG,IAAI,GAAG,EAAE,CAAA;YAC3B,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,eAAe,CAAC,CAAA;QAC9C,CAAC;QAED,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAC5B,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,OAAO,CAAC,OAAO,CAAC,CAAA;IAC5B,CAAC,CAAC,CAAA;IAEF,OAAO,GAAG,EAAE;QACV,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,MAAM,eAAe,GAAG,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;YAEnD,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,CAAC,OAAO,CAAC,CAAA;YAEhC,IAAI,CAAC,CAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,IAAI,CAAA,EAAE,CAAC;gBAC3B,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,SAAS,CAAC,OAAO,CAAC,CAAA;YAC9B,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC,CAAA;AACH,CAAC"}