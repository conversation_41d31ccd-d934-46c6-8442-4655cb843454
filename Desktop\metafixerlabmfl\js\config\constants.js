// ===============================================
// CONFIGURATION ET CONSTANTES - PlayMFL Manager
// Version corrigée avec les vrais codes de position PlayMFL
// ===============================================

// Configuration API
const CONFIG = {
    API_BASE_URL: window.location.hostname === 'localhost' 
        ? "http://localhost:3000/api"
        : `${window.location.origin}/api`,
    
    PLAYMFL_BASE_URL: 'https://app.playmfl.com',
    CDN_BASE_URL: 'https://d13e14gtps4iwl.cloudfront.net',
    
    // Limites et seuils
    DEFAULT_FATIGUE_THRESHOLD: 94,
    MAX_ENERGY: 10000,
    TOKEN_CHECK_INTERVAL: 5 * 60 * 1000, // 5 minutes
    
    // Timeouts des messages
    MESSAGE_TIMEOUTS: {
        success: 6000,
        error: 5000,
        warning: 8000,
        info: 20000
    }
};

// Mapping des divisions
const DIVISION_NAMES = {
    1: 'Diamant',
    2: 'Platine', 
    3: 'Or',
    4: 'Argent',
    5: 'Bronze',
    6: 'Fer',
    7: 'Pierre',
    8: 'Glace',
    9: 'Spark',
    10: 'Flint'
};

// Règles tactiques
const TACTIC_RULES = {
    // Sliders continus
    width: { 
        type: 'slider', 
        min: 0.0, 
        max: 2.0, 
        step: 0.1, 
        name: 'Largeur d\'attaque' 
    },
    depth: { 
        type: 'slider', 
        min: 0.0, 
        max: 1.3, 
        step: 0.1, 
        name: 'Profondeur de l\'équipe' 
    },
    compactness: { 
        type: 'slider', 
        min: 0.0, 
        max: 1.3, 
        step: 0.1, 
        name: 'Largeur du bloc défensif' 
    },
    
    // Boutons 3 états
    directness: { 
        type: 'buttons3', 
        values: [0.7, null, 1.3], 
        labels: ['CONTRÔLÉ', 'ÉQUILIBRÉ', 'DIRECT'],
        display: [-1, 0, 1],
        name: 'Caractère direct des passes' 
    },
    pressing: { 
        type: 'buttons3', 
        values: [0.5, null, 1.5], 
        labels: ['MOINS SOUVENT', 'ÉQUILIBRÉ', 'PLUS SOUVENT'],
        display: [-1, 0, 1],
        name: 'Déclenchement du pressing' 
    },
    crosses: { 
        type: 'buttons3', 
        values: [0.5, null, 1.5], 
        labels: ['CENTRER MOINS SOUVENT', 'ÉQUILIBRÉ', 'CENTRER PLUS SOUVENT'],
        display: [-1, 0, 1],
        name: 'Centres' 
    },
    dribble: { 
        type: 'buttons3', 
        values: [0.5, null, 1.5], 
        labels: ['DRIBBLER MOINS SOUVENT', 'ÉQUILIBRÉ', 'DRIBBLER PLUS SOUVENT'],
        display: [-1, 0, 1],
        name: 'Dribbles' 
    },
    aggressivity: { 
        type: 'buttons3', 
        values: [0, null, 2], 
        labels: ['MOINS AGRESSIF', 'ÉQUILIBRÉ', 'PLUS AGRESSIF'],
        display: [-1, 0, 1],
        name: 'Agressivité' 
    },
    clearance: { 
        type: 'buttons3', 
        values: [0, null, 2], 
        labels: ['JOUER DEPUIS LA DÉFENSE', 'ÉQUILIBRÉ', 'DÉGAGER LE BALLON SOUVENT'],
        display: [-1, 0, 1],
        name: 'Dégagements' 
    },
    sideAttackLeft: { 
        type: 'buttons3', 
        values: [0.0, null, 1.5], 
        labels: ['PRIORISER CÔTÉ FAIBLE', 'ÉQUILIBRÉ', 'PRIORISER CÔTÉ DROIT'],
        display: [-1, 0, 1],
        name: 'Préférence de côté' 
    },
    farShot: { 
        type: 'buttons3', 
        values: [0, null, 2], 
        labels: ['PRENDRE MOINS DE TIRS DE LOIN', 'ÉQUILIBRÉ', 'PRENDRE PLUS DE TIRS DE LOIN'],
        display: [-1, 0, 1],
        name: 'Tirs de loin' 
    },
    tempo: { 
        type: 'buttons3', 
        values: [0.5, null, 1.5], 
        labels: ['TEMPO LENT', 'ÉQUILIBRÉ', 'TEMPO RAPIDE'],
        display: [-1, 0, 1],
        name: 'Tempo de jeu' 
    },
    offside: { 
        type: 'buttons3', 
        values: [0, null, 2], 
        labels: ['ÉVITER LE PIÈGE', 'ÉQUILIBRÉ', 'JOUER LE HORS-JEU'],
        display: [-1, 0, 1],
        name: 'Piège du hors-jeu' 
    },
    tackles: { 
        type: 'buttons3', 
        values: [0.5, null, 1.5], 
        labels: ['TACLES PRUDENTS', 'ÉQUILIBRÉ', 'TACLES AGRESSIFS'],
        display: [-1, 0, 1],
        name: 'Intensité des tacles' 
    }
};

// Coefficients de pondération par position
const POSITION_WEIGHTS = {
    // Forward positions
    "ST": { PAS: 0.10, SHO: 0.46, DEF: 0.00, DRI: 0.29, PAC: 0.10, PHY: 0.05, GK: 0.00 },
    "CF": { PAS: 0.24, SHO: 0.23, DEF: 0.00, DRI: 0.40, PAC: 0.13, PHY: 0.00, GK: 0.00 },
    "BU": { PAS: 0.10, SHO: 0.46, DEF: 0.00, DRI: 0.29, PAC: 0.10, PHY: 0.05, GK: 0.00 },
    
    // Winger positions  
    "LW": { PAS: 0.24, SHO: 0.23, DEF: 0.00, DRI: 0.40, PAC: 0.13, PHY: 0.00, GK: 0.00 },
    "RW": { PAS: 0.24, SHO: 0.23, DEF: 0.00, DRI: 0.40, PAC: 0.13, PHY: 0.00, GK: 0.00 },
    "AG": { PAS: 0.24, SHO: 0.23, DEF: 0.00, DRI: 0.40, PAC: 0.13, PHY: 0.00, GK: 0.00 },
    "AD": { PAS: 0.24, SHO: 0.23, DEF: 0.00, DRI: 0.40, PAC: 0.13, PHY: 0.00, GK: 0.00 },
    
    // Attacking Midfielder positions
    "CAM": { PAS: 0.34, SHO: 0.21, DEF: 0.00, DRI: 0.38, PAC: 0.07, PHY: 0.00, GK: 0.00 },
    "AT": { PAS: 0.34, SHO: 0.21, DEF: 0.00, DRI: 0.38, PAC: 0.07, PHY: 0.00, GK: 0.00 },
    "MOC": { PAS: 0.34, SHO: 0.21, DEF: 0.00, DRI: 0.38, PAC: 0.07, PHY: 0.00, GK: 0.00 },
    
    // Central Midfielder positions
    "CM": { PAS: 0.43, SHO: 0.12, DEF: 0.10, DRI: 0.29, PAC: 0.00, PHY: 0.06, GK: 0.00 },
    "MC": { PAS: 0.43, SHO: 0.12, DEF: 0.10, DRI: 0.29, PAC: 0.00, PHY: 0.06, GK: 0.00 },
    
    // Wide Midfielder positions
    "LM": { PAS: 0.43, SHO: 0.12, DEF: 0.10, DRI: 0.29, PAC: 0.00, PHY: 0.06, GK: 0.00 },
    "RM": { PAS: 0.43, SHO: 0.12, DEF: 0.10, DRI: 0.29, PAC: 0.00, PHY: 0.06, GK: 0.00 },
    "MG": { PAS: 0.43, SHO: 0.12, DEF: 0.10, DRI: 0.29, PAC: 0.00, PHY: 0.06, GK: 0.00 },
    "MD": { PAS: 0.43, SHO: 0.12, DEF: 0.10, DRI: 0.29, PAC: 0.00, PHY: 0.06, GK: 0.00 },
    
    // Defensive Midfielder positions
    "CDM": { PAS: 0.28, SHO: 0.00, DEF: 0.40, DRI: 0.17, PAC: 0.00, PHY: 0.15, GK: 0.00 },
    "MDC": { PAS: 0.28, SHO: 0.00, DEF: 0.40, DRI: 0.17, PAC: 0.00, PHY: 0.15, GK: 0.00 },
    
    // Full Back positions
    "LB": { PAS: 0.19, SHO: 0.00, DEF: 0.44, DRI: 0.17, PAC: 0.10, PHY: 0.10, GK: 0.00 },
    "RB": { PAS: 0.19, SHO: 0.00, DEF: 0.44, DRI: 0.17, PAC: 0.10, PHY: 0.10, GK: 0.00 },
    "DG": { PAS: 0.19, SHO: 0.00, DEF: 0.44, DRI: 0.17, PAC: 0.10, PHY: 0.10, GK: 0.00 },
    "DD": { PAS: 0.19, SHO: 0.00, DEF: 0.44, DRI: 0.17, PAC: 0.10, PHY: 0.10, GK: 0.00 },
    
    // Wing Back positions
    "LWB": { PAS: 0.19, SHO: 0.00, DEF: 0.44, DRI: 0.17, PAC: 0.10, PHY: 0.10, GK: 0.00 },
    "RWB": { PAS: 0.19, SHO: 0.00, DEF: 0.44, DRI: 0.17, PAC: 0.10, PHY: 0.10, GK: 0.00 },
    "DLG": { PAS: 0.19, SHO: 0.00, DEF: 0.44, DRI: 0.17, PAC: 0.10, PHY: 0.10, GK: 0.00 },
    "DLD": { PAS: 0.19, SHO: 0.00, DEF: 0.44, DRI: 0.17, PAC: 0.10, PHY: 0.10, GK: 0.00 },
    
    // Center Back positions
    "CB": { PAS: 0.05, SHO: 0.00, DEF: 0.64, DRI: 0.09, PAC: 0.02, PHY: 0.20, GK: 0.00 },
    "DC": { PAS: 0.05, SHO: 0.00, DEF: 0.64, DRI: 0.09, PAC: 0.02, PHY: 0.20, GK: 0.00 },
    
    // Goalkeeper positions
    "GK": { PAS: 0.00, SHO: 0.00, DEF: 0.00, DRI: 0.00, PAC: 0.00, PHY: 0.00, GK: 1.00 },
    "G": { PAS: 0.00, SHO: 0.00, DEF: 0.00, DRI: 0.00, PAC: 0.00, PHY: 0.00, GK: 1.00 }
};

// Mapping des formations vers positions - CORRIGÉ avec les vrais codes PlayMFL
const FORMATION_TO_POSITIONS = {
    // ===============================================
    // FORMATIONS 3 DÉFENSEURS
    // ===============================================
    '3-4-2-1': ['GK', 'CB', 'CB', 'CB', 'LM', 'CM', 'CM', 'RM', 'AT', 'AT', 'ST'],
    '3-4-3': ['GK', 'CB', 'CB', 'CB', 'LM', 'CM', 'CM', 'RM', 'LW', 'ST', 'RW'],
    '3-4-3_diamond': ['GK', 'CB', 'CB', 'CB', 'LM', 'CDM', 'CAM', 'RM', 'LW', 'ST', 'RW'],
    '3-5-2': ['GK', 'DC', 'DC', 'DC', 'MDC', 'MC', 'MC', 'MG', 'MD', 'BU', 'BU'],
    '3-5-2_B': ['G', 'DC', 'DC', 'DC', 'MG', 'MDC', 'MDC', 'MOC', 'MD', 'BU', 'BU'],

    // ===============================================
    // FORMATIONS 4 DÉFENSEURS
    // ===============================================
    '4-1-2-1-2': ['GK', 'LB', 'CB', 'CB', 'RB', 'CDM', 'LM', 'RM', 'CAM', 'ST', 'ST'],
    '4-1-2-1-2_narrow': ['GK', 'RB', 'CB', 'CB', 'LB', 'CDM', 'CM', 'CM', 'ST', 'CAM', 'ST'],
    '4-1-3-2': ['GK', 'LB', 'CB', 'CB', 'RB', 'CDM', 'LM', 'CM', 'RM', 'ST', 'ST'],
    '4-1-4-1': ['GK', 'LB', 'CB', 'CB', 'RB', 'CDM', 'LM', 'CM', 'CM', 'RM', 'ST'],
    '4-2-2-2': ['GK', 'LB', 'CB', 'CB', 'RB', 'CDM', 'CDM', 'CAM', 'CAM', 'ST', 'ST'],
    '4-2-3-1': ['GK', 'LB', 'CB', 'CB', 'RB', 'CDM', 'CDM', 'CAM', 'LM', 'RM', 'ST'],
    '4-2-4': ['GK', 'LB', 'CB', 'CB', 'RB', 'CM', 'CM', 'LW', 'ST', 'ST', 'RW'],
    '4-3-1-2': ['GK', 'LB', 'CB', 'CB', 'RB', 'CM', 'CM', 'CM', 'CAM', 'ST', 'ST'],
    '4-3-2-1': ['G', 'DG', 'DC', 'DC', 'DD', 'MC', 'MC', 'MC', 'AT', 'AT', 'BU'],
    '4-3-3': ['GK', 'LB', 'CB', 'CB', 'RB', 'CM', 'CM', 'CM', 'LW', 'ST', 'RW'],
    '4-3-3_att': ['GK', 'LB', 'CB', 'CB', 'RB', 'CM', 'CM', 'CAM', 'LW', 'ST', 'RW'],
    '4-3-3_def': ['GK', 'LB', 'CB', 'CB', 'RB', 'CDM', 'CM', 'CM', 'LW', 'ST', 'RW'],
    '4-3-3_false_9': ['G', 'DG', 'DC', 'DC', 'DD', 'MDC', 'MC', 'MC', 'AG', 'AT', 'AD'],

    // ===============================================
    // FORMATIONS 5 DÉFENSEURS
    // ===============================================
    '5-2-1-2': ['GK', 'LWB', 'CB', 'CB', 'CB', 'RWB', 'CM', 'CM', 'CAM', 'ST', 'ST'],
    '5-2-2-1': ['GK', 'LWB', 'CB', 'CB', 'CB', 'RWB', 'CM', 'CM', 'CAM', 'CAM', 'ST'],
    '5-3-2': ['G', 'DLG', 'DC', 'DC', 'DC', 'DLD', 'MG', 'MC', 'MD', 'BU', 'BU'],
    '5-4-1': ['G', 'DLD', 'DC', 'DC', 'DC', 'DLG', 'MD', 'MDC', 'MG', 'MOC', 'BU'],

    // ===============================================
    // FORMATIONS CLASSIQUES (COMPATIBILITÉ)
    // ===============================================
    '4-4-2': ['GK', 'LB', 'CB', 'CB', 'RB', 'LM', 'CM', 'CM', 'RM', 'ST', 'ST'],
    '4-4-2_B': ['GK', 'LB', 'CB', 'CB', 'RB', 'LM', 'CM', 'CM', 'RM', 'ST', 'ST']
};

// ===============================================
// MAPPING MANUEL DES FORMATIONS - ÉDITABLE
// ===============================================
// 🎯 INSTRUCTIONS :
// - Modifiez les mappings ci-dessous selon vos observations dans PlayMFL
// - Index 0 = Gardien, puis suivez l'ordre visuel de la formation
// - ⚠️ IMPORTANT : Utilisez UNIQUEMENT les codes FRANÇAIS : G, DLG, DC, DLD, MG, MDC, MD, MOC, BU, etc.
// - ❌ N'utilisez PAS les codes anglais : GK, LB, CB, RB, CDM, CM, CAM, ST, etc.
// - Les mappings manuels ont PRIORITÉ sur les mappings automatiques

const MANUAL_FORMATION_MAPPINGS = {
    // ===============================================
    // FORMATIONS VÉRIFIÉES ET CORRIGÉES
    // ===============================================

    // Formation 5-4-1 (L'Icaunique) ✅ VÉRIFIÉ
    '5-4-1': ['G', 'DLD', 'DC', 'DC', 'DC', 'DLG', 'MD', 'MDC', 'MG', 'MOC', 'BU'],
    //         0     1     2     3     4     5     6     7      8     9     10

    // Formation 4-1-2-1-2_narrow (K-Socios) ✅ VÉRIFIÉ
    '4-1-2-1-2_narrow': ['G', 'DD', 'DC', 'DC', 'DG', 'MDC', 'MC', 'MC', 'BU', 'MOC', 'BU'],
    //                    0    1     2     3     4     5      6     7     8     9      10

    // ===============================================
    // FORMATIONS À VÉRIFIER
    // ===============================================
    // Ajoutez ici les formations que vous voulez corriger manuellement
    // Exemple :
    // '4-3-3': ['GK', 'LB', 'CB', 'CB', 'RB', 'CM', 'CM', 'CM', 'LW', 'ST', 'RW'],

    // ===============================================
    // FORMATIONS PROBLÉMATIQUES CONNUES
    // ===============================================
    // Décommentez et corrigez selon vos observations :

    // '4-4-2': ['GK', 'LB', 'CB', 'CB', 'RB', 'LM', 'CM', 'CM', 'RM', 'ST', 'ST'],
    // '4-3-3': ['GK', 'LB', 'CB', 'CB', 'RB', 'CM', 'CM', 'CM', 'LW', 'ST', 'RW'],
    // '3-5-2': ['GK', 'DC', 'DC', 'DC', 'MDC', 'MC', 'MC', 'MG', 'MD', 'BU', 'BU'],
};

// Fonction pour obtenir le mapping d'une formation (priorité au manuel)
function getFormationMapping(formationType) {
    // 1. Vérifier d'abord le mapping manuel
    if (MANUAL_FORMATION_MAPPINGS[formationType]) {
        console.log(`📋 Utilisation du mapping MANUEL pour ${formationType}`);
        return MANUAL_FORMATION_MAPPINGS[formationType];
    }

    // 2. Sinon utiliser le mapping automatique
    if (FORMATION_TO_POSITIONS[formationType]) {
        console.log(`🤖 Utilisation du mapping AUTOMATIQUE pour ${formationType}`);
        return FORMATION_TO_POSITIONS[formationType];
    }

    // 3. Fallback par défaut
    console.warn(`⚠️ Aucun mapping trouvé pour ${formationType}, utilisation du fallback`);
    return ['GK', 'LB', 'CB', 'CB', 'RB', 'CM', 'CM', 'CM', 'LW', 'ST', 'RW'];
}

// Groupes de positions similaires - ÉTENDU avec les codes PlayMFL
const POSITION_GROUPS = {
    'GK': ['GK', 'G'],
    'CB': ['CB', 'DC'],
    'LB': ['LB', 'DG', 'LWB', 'DLG'],
    'RB': ['RB', 'DD', 'RWB', 'DLD'],
    'CDM': ['CDM', 'MDC'],
    'CM': ['CM', 'MC'],
    'LM': ['LM', 'MG'],
    'RM': ['RM', 'MD'],
    'CAM': ['CAM', 'MOC'],
    'LW': ['LW', 'AG'],
    'RW': ['RW', 'AD'],
    'ST': ['ST', 'BU'],
    'CF': ['CF', 'AT']  // AT (français) = CF (anglais) = même poste
};

// ===============================================
// SYSTÈME DE FAMILIARITÉ POSITIONNELLE PLAYMFL
// ===============================================

// Pénalités selon la familiarité (appliquées à tous les attributs)
const FAMILIARITY_PENALTIES = {
    PRIMARY: 0,           // Position principale
    SECONDARY: -1,        // Position secondaire/tertiaire
    FAIRLY_FAMILIAR: -5,  // Assez familier
    SOMEWHAT_FAMILIAR: -8, // Peu familier
    UNFAMILIAR: -20       // Non familier
};

// Matrice de familiarité entre positions
const POSITION_FAMILIARITY = {
    // Gardiens
    'GK': { 'GK': 'PRIMARY' },

    // Défenseurs centraux
    'CB': {
        'CB': 'PRIMARY',
        'LB': 'FAIRLY_FAMILIAR', 'RB': 'FAIRLY_FAMILIAR',
        'CDM': 'FAIRLY_FAMILIAR'
    },

    // Latéraux
    'LB': {
        'LB': 'PRIMARY', 'LWB': 'SECONDARY',
        'CB': 'FAIRLY_FAMILIAR', 'LM': 'FAIRLY_FAMILIAR',
        'RB': 'SOMEWHAT_FAMILIAR'
    },
    'RB': {
        'RB': 'PRIMARY', 'RWB': 'SECONDARY',
        'CB': 'FAIRLY_FAMILIAR', 'RM': 'FAIRLY_FAMILIAR',
        'LB': 'SOMEWHAT_FAMILIAR'
    },
    'LWB': {
        'LWB': 'PRIMARY', 'LB': 'SECONDARY',
        'LM': 'FAIRLY_FAMILIAR', 'LW': 'FAIRLY_FAMILIAR'
    },
    'RWB': {
        'RWB': 'PRIMARY', 'RB': 'SECONDARY',
        'RM': 'FAIRLY_FAMILIAR', 'RW': 'FAIRLY_FAMILIAR'
    },

    // Milieux défensifs
    'CDM': {
        'CDM': 'PRIMARY',
        'CM': 'FAIRLY_FAMILIAR', 'CB': 'FAIRLY_FAMILIAR'
    },

    // Milieux centraux
    'CM': {
        'CM': 'PRIMARY',
        'CDM': 'FAIRLY_FAMILIAR', 'CAM': 'FAIRLY_FAMILIAR',
        'LM': 'FAIRLY_FAMILIAR', 'RM': 'FAIRLY_FAMILIAR'
    },

    // Milieux latéraux
    'LM': {
        'LM': 'PRIMARY',
        'CM': 'FAIRLY_FAMILIAR', 'LW': 'FAIRLY_FAMILIAR',
        'LB': 'FAIRLY_FAMILIAR', 'LWB': 'FAIRLY_FAMILIAR'
    },
    'RM': {
        'RM': 'PRIMARY',
        'CM': 'FAIRLY_FAMILIAR', 'RW': 'FAIRLY_FAMILIAR',
        'RB': 'FAIRLY_FAMILIAR', 'RWB': 'FAIRLY_FAMILIAR'
    },

    // Milieux offensifs
    'CAM': {
        'CAM': 'PRIMARY',
        'CM': 'FAIRLY_FAMILIAR', 'CF': 'FAIRLY_FAMILIAR',
        'LW': 'SOMEWHAT_FAMILIAR', 'RW': 'SOMEWHAT_FAMILIAR'
    },

    // Ailiers
    'LW': {
        'LW': 'PRIMARY',
        'LM': 'FAIRLY_FAMILIAR', 'CF': 'FAIRLY_FAMILIAR',
        'ST': 'SOMEWHAT_FAMILIAR', 'CAM': 'SOMEWHAT_FAMILIAR'
    },
    'RW': {
        'RW': 'PRIMARY',
        'RM': 'FAIRLY_FAMILIAR', 'CF': 'FAIRLY_FAMILIAR',
        'ST': 'SOMEWHAT_FAMILIAR', 'CAM': 'SOMEWHAT_FAMILIAR'
    },

    // Attaquants
    'CF': {
        'CF': 'PRIMARY',
        'ST': 'SECONDARY', 'CAM': 'FAIRLY_FAMILIAR',
        'LW': 'FAIRLY_FAMILIAR', 'RW': 'FAIRLY_FAMILIAR'
    },
    'ST': {
        'ST': 'PRIMARY',
        'CF': 'SECONDARY',
        'LW': 'SOMEWHAT_FAMILIAR', 'RW': 'SOMEWHAT_FAMILIAR'
    }
};

console.log('✅ Configuration et constantes chargées (version PlayMFL corrigée)');