// server.js - API Node.js pour l'authentification Dapper
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Store temporaire des sessions (en production, utilisez Redis ou MongoDB)
const sessions = new Map();

// ===============================================
// ROUTES DAPPER AUTHENTICATION
// ===============================================

// Route pour initier l'authentification Dapper
app.post('/api/auth/dapper/init', (req, res) => {
    try {
        const { walletAddress } = req.body;
        
        if (!walletAddress) {
            return res.status(400).json({ 
                error: 'Wallet address required' 
            });
        }
        
        console.log('🔐 Initiation auth Dapper pour:', walletAddress);
        
        // Générer un token de session temporaire
        const sessionToken = `dpr_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
        
        // Stocker la session
        sessions.set(sessionToken, {
            walletAddress,
            createdAt: Date.now(),
            verified: false
        });
        
        res.json({
            success: true,
            sessionToken,
            message: 'Session initiée'
        });
        
    } catch (error) {
        console.error('❌ Erreur init Dapper:', error);
        res.status(500).json({ 
            error: 'Erreur serveur',
            details: error.message 
        });
    }
});

// Route pour vérifier et finaliser l'authentification
app.post('/api/auth/dapper/verify', (req, res) => {
    try {
        const { sessionToken, walletAddress } = req.body;
        
        if (!sessionToken || !walletAddress) {
            return res.status(400).json({ 
                error: 'Session token and wallet address required' 
            });
        }
        
        const session = sessions.get(sessionToken);
        
        if (!session) {
            return res.status(404).json({ 
                error: 'Session non trouvée' 
            });
        }
        
        if (session.walletAddress !== walletAddress) {
            return res.status(403).json({ 
                error: 'Wallet address mismatch' 
            });
        }
        
        // Vérifier que la session n'est pas trop ancienne (5 minutes max)
        const sessionAge = Date.now() - session.createdAt;
        if (sessionAge > 5 * 60 * 1000) {
            sessions.delete(sessionToken);
            return res.status(410).json({ 
                error: 'Session expirée' 
            });
        }
        
        // Marquer comme vérifiée
        session.verified = true;
        sessions.set(sessionToken, session);
        
        // Générer le token PlayMFL final
        const playmflToken = generatePlayMFLToken(walletAddress);
        
        console.log('✅ Auth Dapper vérifiée pour:', walletAddress);
        
        res.json({
            success: true,
            playmflToken,
            walletAddress,
            message: 'Authentification réussie'
        });
        
    } catch (error) {
        console.error('❌ Erreur verify Dapper:', error);
        res.status(500).json({ 
            error: 'Erreur serveur',
            details: error.message 
        });
    }
});

// Route pour obtenir le statut d'une session
app.get('/api/auth/dapper/status/:sessionToken', (req, res) => {
    try {
        const { sessionToken } = req.params;
        const session = sessions.get(sessionToken);
        
        if (!session) {
            return res.status(404).json({ 
                error: 'Session non trouvée' 
            });
        }
        
        res.json({
            success: true,
            verified: session.verified,
            walletAddress: session.walletAddress,
            createdAt: session.createdAt
        });
        
    } catch (error) {
        console.error('❌ Erreur status Dapper:', error);
        res.status(500).json({ 
            error: 'Erreur serveur',
            details: error.message 
        });
    }
});

// ===============================================
// UTILITAIRES
// ===============================================

function generatePlayMFLToken(walletAddress) {
    // En production, vous pourriez :
    // 1. Appeler l'API PlayMFL officielle
    // 2. Générer un JWT signé
    // 3. Utiliser une base de données
    
    const timestamp = Date.now();
    const randomPart = Math.random().toString(36).substring(2, 15);
    const addressPart = walletAddress.slice(2, 10);
    
    return `dpr_${addressPart}_${randomPart}_${timestamp.toString(36)}`;
}

// Nettoyage périodique des sessions expirées
setInterval(() => {
    const now = Date.now();
    for (const [token, session] of sessions.entries()) {
        const age = now - session.createdAt;
        if (age > 10 * 60 * 1000) { // 10 minutes
            sessions.delete(token);
            console.log('🧹 Session expirée supprimée:', token);
        }
    }
}, 60 * 1000); // Nettoyer toutes les minutes

// ===============================================
// ROUTES STATIC
// ===============================================

app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// ===============================================
// DÉMARRAGE SERVEUR
// ===============================================

app.listen(PORT, () => {
    console.log('🚀 ========================================');
    console.log('🎯 Serveur Dapper Auth démarré !');
    console.log('🌐 URL: http://localhost:' + PORT);
    console.log('🔗 API: http://localhost:' + PORT + '/api');
    console.log('========================================');
});

// Gestion propre de l'arrêt
process.on('SIGINT', () => {
    console.log('\n👋 Arrêt du serveur...');
    process.exit(0);
});