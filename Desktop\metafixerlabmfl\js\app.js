// ===============================================
// APPLICATION PRINCIPALE - PlayMFL Manager
// Version avec intégration Dapper Wallet
// ===============================================
// Note: FCL est chargé via CDN dans index.html


const App = {
    
    // Variables globales
    userTeams: [],
    allClubPlayers: {},
    namedFormations: {},
    
    // Éléments DOM
    elements: {},
    
    // ===============================================
    // INITIALISATION
    // ===============================================
    
    init() {
        console.log('🚀 PlayMFL Manager - Initialisation...');
        
        // Initialiser UIManager en premier
        UIManager.init();
        
        this.initElements();
        this.initEventListeners();
        this.loadSavedData();
        
        // Vérifier si on était connecté via Dapper
        this.checkPreviousSession();
        
        UIManager.updateStatus(false, 'Déconnecté');
        console.log('✅ Application initialisée');
    },

    initElements() {
        this.elements = {
            tokenInput: document.getElementById('tokenInput'),
            connectBtn: document.getElementById('connectBtn'),
            dapperBtn: document.getElementById('dapperConnectBtn'),
            statusDot: document.getElementById('statusDot'),
            statusText: document.getElementById('statusText'),
            messageArea: document.getElementById('messageArea'),
            teamsContainer: document.getElementById('teamsContainer'),
            teamsAccordion: document.getElementById('teamsAccordion'),
            globalControls: document.getElementById('globalControls'),
            fatigueThreshold: document.getElementById('fatigueThreshold')
        };
    },

    initEventListeners() {
        // Connexion manuelle
        this.elements.tokenInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.handleConnection();
        });
        this.elements.connectBtn.addEventListener('click', () => this.handleConnection());
        
        // Seuil de fatigue
        if (this.elements.fatigueThreshold) {
            this.elements.fatigueThreshold.addEventListener('change', () => this.handleFatigueThresholdChange());
        }
    },

    loadSavedData() {
        // Charger le token sauvegardé
        const savedToken = StorageManager.loadSavedToken();
        if (savedToken) {
            this.elements.tokenInput.value = savedToken;
        }
        
        // Charger les paramètres
        const settings = StorageManager.loadSettings();
        if (this.elements.fatigueThreshold) {
            this.elements.fatigueThreshold.value = settings.fatigueThreshold;
        }
    },
    
    checkPreviousSession() {
        // Vérifier le type de connexion précédent
        const connectionType = StorageManager.loadData('connectionType');
        const savedToken = StorageManager.loadSavedToken();
        
        if (connectionType === 'dapper' && savedToken) {
            console.log('🔄 Session Dapper précédente détectée');
            
            // Si FCL est chargé, tenter de restaurer la session
            if (typeof fcl !== 'undefined') {
                // Vérifier si déjà connecté à Dapper
                const user = fcl.currentUser.snapshot();
                if (user && user.loggedIn) {
                    console.log('✅ Session Dapper toujours active');
                    // Déjà connecté, utiliser le token sauvegardé
                    this.elements.tokenInput.value = savedToken;
                    
                    // Connecter automatiquement
                    setTimeout(() => {
                        this.handleConnection();
                    }, 500);
                } else {
                    console.log('🔑 Session Dapper expirée, reconnexion nécessaire');
                }
            }
        } else if (savedToken) {
            console.log('🔄 Session manuelle précédente détectée');
            // Connexion manuelle précédente, mais pas de reconnexion auto
        }
    },

    // ===============================================
    // GESTION DE LA CONNEXION
    // ===============================================
    
    async handleConnection() {
        const token = this.elements.tokenInput.value.trim();
        if (!token) {
            UIManager.showMessage('error', 'Token obligatoire');
            return;
        }

        this.elements.connectBtn.innerHTML = '<div class="loading"></div> Connexion...';
        this.elements.connectBtn.disabled = true;

        try {
            const result = await ApiManager.loadTeams(token);
            
            this.userTeams = result.teams;
            await this.loadAdvancedDataOptimized();
            
            UIManager.displayTeams(this.userTeams);
            UIManager.updateStatus(true, `${result.teams.length} équipe(s) chargée(s)`);
            UIManager.showMessage('success', `🎉 ${result.teams.length} équipe(s) trouvée(s) dans ${result.clubCount} club(s) (${result.loadTime}s)`);
            
            // Mettre à jour l'interface Dapper si la connexion est venue de Dapper
            if (token.startsWith('dapper_') && this.elements.dapperBtn) {
                this.elements.dapperBtn.disabled = true;
                this.elements.dapperBtn.innerHTML = '✅ Dapper connecté';
            }
            
            ApiManager.startTokenMonitoring();

        } catch (error) {
            console.error('❌ Erreur lors de la connexion:', error);
            UIManager.updateStatus(false, 'Échec de la connexion');
            UIManager.showMessage('error', `Erreur: ${error.message}`);
            
            ApiManager.stopTokenMonitoring();
            this.elements.connectBtn.textContent = 'Se connecter';
            this.elements.connectBtn.disabled = false;
            
            // Réinitialiser le bouton Dapper si c'était une tentative de connexion Dapper
            if (token.startsWith('dapper_') && this.elements.dapperBtn) {
                this.elements.dapperBtn.innerHTML = '🔥 Connexion avec Dapper Wallet';
                this.elements.dapperBtn.disabled = false;
            }
        }
    },

    // ===============================================
    // GESTION DE LA DÉCONNEXION - NOUVEAU
    // ===============================================
    
    handleLogout() {
        ApiManager.stopTokenMonitoring();
        
        // Réinitialiser l'interface
        UIManager.updateStatus(false, 'Déconnecté');
        this.elements.teamsContainer.classList.remove('show');
        this.elements.globalControls.style.display = 'none';
        
        // Vider le token
        this.elements.tokenInput.value = '';
        this.elements.connectBtn.textContent = 'Se connecter';
        this.elements.connectBtn.disabled = false;
        
        // Réinitialiser le bouton Dapper
        if (this.elements.dapperBtn) {
            this.elements.dapperBtn.innerHTML = '🔥 Connexion avec Dapper Wallet';
            this.elements.dapperBtn.disabled = false;
        }
        
        // Déconnecter Dapper si nécessaire
        if (typeof window.logoutDapper === 'function') {
            window.logoutDapper();
        }
        
        // Effacer le token stocké
        StorageManager.clearToken();
        StorageManager.removeData('connectionType');
        
        UIManager.showMessage('info', 'Vous avez été déconnecté');
        console.log('👋 Utilisateur déconnecté');
    },

    // ===============================================
    // CHARGEMENT DES DONNÉES AVANCÉES
    // ===============================================
    
    async loadAdvancedDataOptimized() {
        console.log('📊 Chargement des données avancées (parallélisé)...');
        
        // Grouper les équipes par club pour éviter les doublons
        const clubIds = [...new Set(this.userTeams.map(team => team.clubId))];
        
        // Charger tous les joueurs de tous les clubs en parallèle
        const playersPromises = clubIds.map(async (clubId) => {
            if (!this.allClubPlayers[clubId]) {
                const players = await ApiManager.loadClubPlayers(clubId);
                this.allClubPlayers[clubId] = players;
            }
        });
        
        // Charger tous les préréglages de toutes les équipes en parallèle
        const formationsPromises = this.userTeams.map(async (team) => {
            const formations = await ApiManager.loadNamedFormations(team.clubId, team.squadId);
            this.namedFormations[`${team.clubId}-${team.squadId}`] = formations;
        });
        
        // Attendre tout en parallèle
        await Promise.all([
            ...playersPromises,
            ...formationsPromises
        ]);
        
        console.log('✅ Données avancées chargées en parallèle');
    },

    // ===============================================
    // GESTIONNAIRES D'ÉVÉNEMENTS
    // ===============================================
    
    handleFatigueThresholdChange() {
        if (!this.elements.fatigueThreshold) return;
        
        const threshold = parseInt(this.elements.fatigueThreshold.value);
        if (threshold < 0 || threshold > 100) {
            UIManager.showMessage('error', 'Le seuil doit être entre 0 et 100%');
            return;
        }
        
        const settings = StorageManager.loadSettings();
        settings.fatigueThreshold = threshold;
        StorageManager.saveSettings(settings);
        
        UIManager.showMessage('success', `✅ Seuil de fatigue mis à jour: ${threshold}%`);
        console.log(`🎯 Nouveau seuil de fatigue global: ${threshold}%`);
    },

    // ===============================================
    // ACTIONS GLOBALES
    // ===============================================
    
    async rotateAllTeamsFatigue() {
        try {
            UIManager.showMessage('info', '🔄 Rotation en cours sur toutes les équipes...');
            const results = await RotationSystem.rotateAllTeams('fatigue');
            
            const summary = RotationSystem.getRotationSummary(results.results);
            UIManager.showMessage('success', `✅ Rotation fatigue terminée: ${summary}`);
            
        } catch (error) {
            console.error('❌ Erreur rotation globale:', error);
            UIManager.showMessage('error', `❌ Erreur rotation: ${error.message}`);
        }
    },

    async optimizeAllTeams() {
        try {
            UIManager.showMessage('info', '🧠 Optimisation en cours sur toutes les équipes...');
            const results = await RotationSystem.optimizeAllTeams();
            
            UIManager.showMessage('success', 
                `✅ Optimisation terminée: ${results.successfulTeams}/${results.totalTeams} équipes optimisées (Note moyenne: ${results.averageRating})`
            );
            
        } catch (error) {
            console.error('❌ Erreur optimisation globale:', error);
            UIManager.showMessage('error', `❌ Erreur optimisation: ${error.message}`);
        }
    },

    // ===============================================
    // GETTERS POUR LES AUTRES MODULES
    // ===============================================
    
    getTeam(index) {
        return this.userTeams[index];
    },

    getAllTeams() {
        return this.userTeams;
    },

    getClubPlayers(clubId) {
        return this.allClubPlayers[clubId] || [];
    },

    getNamedFormations(clubId, squadId) {
        return this.namedFormations[`${clubId}-${squadId}`] || [];
    },

    updateTeam(index, updatedTeam) {
        if (index >= 0 && index < this.userTeams.length) {
            this.userTeams[index] = updatedTeam;
        }
    },

    // ===============================================
    // UTILITAIRES
    // ===============================================
    
    getDivisionName(divisionNumber) {
        if (!divisionNumber || typeof divisionNumber === 'string') {
            return divisionNumber || 'Non classé';
        }
        
        const divNum = parseInt(divisionNumber);
        return DIVISION_NAMES[divNum] || `Division ${divisionNumber}`;
    },

    getPlayerCardUrl(playerId, overall) {
        return `${CONFIG.CDN_BASE_URL}/players/${playerId}/card_512.png?co=${overall}`;
    },

    // ===============================================
    // NETTOYAGE
    // ===============================================
    
    cleanup() {
        ApiManager.stopTokenMonitoring();
        console.log('🧹 Application nettoyée');
    }
};

// Manager global pour les équipes (référence pour les autres modules)
const TeamManager = {
    getTeam: (index) => App.getTeam(index),
    getAllTeams: () => App.getAllTeams(),
    getClubPlayers: (clubId) => App.getClubPlayers(clubId),
    getNamedFormations: (clubId, squadId) => App.getNamedFormations(clubId, squadId),
    updateTeam: (index, updatedTeam) => App.updateTeam(index, updatedTeam)
};

// ===============================================
// INITIALISATION AU CHARGEMENT DE LA PAGE
// ===============================================

document.addEventListener('DOMContentLoaded', function() {
    App.init();
    
    // Ajouter un bouton de déconnexion global si nécessaire
    const header = document.querySelector('.header');
    if (header) {
        const logoutBtn = document.createElement('button');
        logoutBtn.className = 'logout-btn';
        logoutBtn.innerHTML = '🚪 Déconnexion';
        logoutBtn.style.display = 'none';
        logoutBtn.addEventListener('click', () => App.handleLogout());
        
        // Observer les changements de statut pour afficher/masquer le bouton
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.target.id === 'statusText') {
                    const isConnected = mutation.target.textContent !== 'Déconnecté';
                    logoutBtn.style.display = isConnected ? 'block' : 'none';
                }
            });
        });
        
        observer.observe(document.getElementById('statusText'), { 
            childList: true,
            characterData: true,
            subtree: true
        });
        
        // Ajouter à l'interface
        header.appendChild(logoutBtn);
    }
});

// Nettoyage avant fermeture
window.addEventListener('beforeunload', function() {
    App.cleanup();
});

console.log('✅ Application principale chargée (version Dapper)');