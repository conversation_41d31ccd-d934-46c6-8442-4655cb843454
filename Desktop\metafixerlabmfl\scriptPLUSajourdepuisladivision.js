// ===============================================
// SCRIPT.JS - PARTIE 1: CONFIGURATION ET CHARGEMENT
// ===============================================

// Configuration
const API_BASE_URL = "http://localhost:3000/api";

// Mapping des divisions numériques vers des noms plus parlants
const DIVISION_NAMES = {
    1: 'Diamant',
    2: 'Platine', 
    3: 'Or',
    4: 'Argent',
    5: 'Bronze',
    6: 'Fer',
    7: 'Pierre',
    8: 'Glace',
    9: 'Spark'
};

// Fonction utilitaire pour obtenir le nom d'une division
function getDivisionName(divisionNumber) {
    // Si c'est déjà un string ou null/undefined, le retourner tel quel
    if (!divisionNumber || typeof divisionNumber === 'string') {
        return divisionNumber || 'Non classé';
    }
    
    // Convertir en nombre et mapper
    const divNum = parseInt(divisionNumber);
    return DIVISION_NAMES[divNum] || `Division ${divisionNumber}`;
}

// Règles de validation RÉELLES basées sur vos captures PlayMFL
const TACTIC_RULES = {
    // SLIDERS CONTINUS (réglage fin avec flèches) - LIMITES AJUSTÉES
    width: { type: 'slider', min: 0.0, max: 2.0, step: 0.1, name: 'Largeur d\'attaque' },
    depth: { type: 'slider', min: 0.0, max: 1.3, step: 0.1, name: 'Profondeur de l\'équipe' },
    compactness: { type: 'slider', min: 0.0, max: 1.3, step: 0.1, name: 'Largeur du bloc défensif' },
    
    // BOUTONS 3 ÉTATS (affichage -1/0/1, mode intermédiaire = NULL)
    directness: { 
        type: 'buttons3', 
        values: [0.7, null, 1.3], 
        labels: ['CONTRÔLÉ', 'ÉQUILIBRÉ', 'DIRECT'],
        display: [-1, 0, 1],
        name: 'Caractère direct des passes' 
    },
    pressing: { 
        type: 'buttons3', 
        values: [0.5, null, 1.5], 
        labels: ['MOINS SOUVENT', 'ÉQUILIBRÉ', 'PLUS SOUVENT'],
        display: [-1, 0, 1],
        name: 'Déclenchement du pressing' 
    },
    crosses: { 
        type: 'buttons3', 
        values: [0.5, null, 1.5], 
        labels: ['CENTRER MOINS SOUVENT', 'ÉQUILIBRÉ', 'CENTRER PLUS SOUVENT'],
        display: [-1, 0, 1],
        name: 'Centres' 
    },
    dribble: { 
        type: 'buttons3', 
        values: [0.5, null, 1.5], 
        labels: ['DRIBBLER MOINS SOUVENT', 'ÉQUILIBRÉ', 'DRIBBLER PLUS SOUVENT'],
        display: [-1, 0, 1],
        name: 'Dribbles' 
    },
    aggressivity: { 
        type: 'buttons3', 
        values: [0, null, 2], 
        labels: ['MOINS AGRESSIF', 'ÉQUILIBRÉ', 'PLUS AGRESSIF'],
        display: [-1, 0, 1],
        name: 'Agressivité' 
    },
    clearance: { 
        type: 'buttons3', 
        values: [0, null, 2], 
        labels: ['JOUER DEPUIS LA DÉFENSE', 'ÉQUILIBRÉ', 'DÉGAGER LE BALLON SOUVENT'],
        display: [-1, 0, 1],
        name: 'Dégagements' 
    },
    sideAttackLeft: { 
        type: 'buttons3', 
        values: [0.0, null, 1.5], 
        labels: ['PRIORISER CÔTÉ FAIBLE', 'ÉQUILIBRÉ', 'PRIORISER CÔTÉ DROIT'],
        display: [-1, 0, 1],
        name: 'Préférence de côté' 
    },
    farShot: { 
        type: 'buttons3', 
        values: [0, null, 2], 
        labels: ['PRENDRE MOINS DE TIRS DE LOIN', 'ÉQUILIBRÉ', 'PRENDRE PLUS DE TIRS DE LOIN'],
        display: [-1, 0, 1],
        name: 'Tirs de loin' 
    },
    // AJOUT DES 3 CRITÈRES MANQUANTS
    tempo: { 
        type: 'buttons3', 
        values: [0.5, null, 1.5], 
        labels: ['TEMPO LENT', 'ÉQUILIBRÉ', 'TEMPO RAPIDE'],
        display: [-1, 0, 1],
        name: 'Tempo de jeu' 
    },
    offside: { 
        type: 'buttons3', 
        values: [0, null, 2], 
        labels: ['ÉVITER LE PIÈGE', 'ÉQUILIBRÉ', 'JOUER LE HORS-JEU'],
        display: [-1, 0, 1],
        name: 'Piège du hors-jeu' 
    },
    tackles: { 
        type: 'buttons3', 
        values: [0.5, null, 1.5], 
        labels: ['TACLES PRUDENTS', 'ÉQUILIBRÉ', 'TACLES AGRESSIFS'],
        display: [-1, 0, 1],
        name: 'Intensité des tacles' 
    }
};

// Variables globales
let currentToken = '';
let userTeams = [];
let allClubPlayers = {}; // Cache des joueurs par club
let namedFormations = {}; // Cache des préréglages par équipe

// Éléments DOM
const tokenInput = document.getElementById('tokenInput');
const connectBtn = document.getElementById('connectBtn');
const statusDot = document.getElementById('statusDot');
const statusText = document.getElementById('statusText');
const messageArea = document.getElementById('messageArea');
const teamsContainer = document.getElementById('teamsContainer');
const teamsAccordion = document.getElementById('teamsAccordion');
const globalControls = document.getElementById('globalControls');
const fatigueThreshold = document.getElementById('fatigueThreshold');

// ===============================================
// INITIALISATION
// ===============================================

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 PlayMFL Manager initialisé');
    
    // Charger le token sauvegardé
    loadSavedToken();
    
    // Événements
    tokenInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') loadTeams();
    });
    connectBtn.addEventListener('click', loadTeams);
    
    updateStatus(false, 'Déconnecté');
});

// ===============================================
// UTILITAIRES DE BASE
// ===============================================

function showMessage(type, message) {
    messageArea.innerHTML = `<div class="message ${type}">${message}</div>`;
    
    // Temps d'affichage selon le type de message
    let timeout = 5000; // 5 secondes par défaut
    if (type === 'info') timeout = 20000; // 20 secondes pour les instructions
    if (type === 'warning') timeout = 8000; // 8 secondes pour les avertissements
    if (type === 'success') timeout = 6000; // 6 secondes pour les succès
    
    setTimeout(() => messageArea.innerHTML = '', timeout);
}

// Fonction pour charger la formation d'une équipe
async function loadFormationForSquad(clubId, squadId) {
    try {
        const response = await fetchWithAuth(`/clubs/${clubId}/squads/${squadId}/formation`);
        if (response.ok) {
            return await response.json();
        }
    } catch (error) {
        console.warn(`⚠️ Formation inaccessible pour squad ${squadId}`);
    }
    return null;
}


function updateStatus(connected, message) {
    statusDot.className = `status-dot ${connected ? 'connected' : ''}`;
    statusText.textContent = message;
    connectBtn.textContent = connected ? 'Connecté ✓' : 'Se connecter';
    connectBtn.disabled = connected;
    teamsContainer.className = `teams-container ${connected ? 'show' : ''}`;
    globalControls.style.display = connected ? 'block' : 'none';
}

async function fetchWithAuth(endpoint, options = {}) {
    const defaultHeaders = {
        'Authorization': `Bearer ${currentToken}`,
        'Accept': '*/*',
        'Origin': 'https://app.playmfl.com',
        'Referer': 'https://app.playmfl.com/'
    };

    const url = `${API_BASE_URL}${endpoint}`;
    console.log(`🌐 ${options.method || 'GET'} ${url}`);

    const response = await fetch(url, {
        ...options,
        headers: { ...defaultHeaders, ...options.headers }
    });

    console.log(`📡 ${response.status} ${response.statusText}`);
    return response;
}

// ===============================================
// GESTION DU TOKEN
// ===============================================

function saveToken(token) {
    try {
        localStorage.setItem('playmfl_token', token);
        console.log('💾 Token sauvegardé');
    } catch (error) {
        console.warn('⚠️ Impossible de sauvegarder le token:', error);
    }
}

function loadSavedToken() {
    try {
        const savedToken = localStorage.getItem('playmfl_token');
        if (savedToken) {
            tokenInput.value = savedToken;
            console.log('✅ Token rechargé depuis le stockage');
        }
    } catch (error) {
        console.warn('⚠️ Impossible de charger le token sauvé:', error);
    }
}

// Vérification automatique de l'expiration du token
async function checkTokenValidity() {
    if (!currentToken) return false;
    
    try {
        const testResponse = await fetchWithAuth('/clubs?limit=1');
        if (testResponse.status === 401) {
            showMessage('warning', '⚠️ Token expiré, veuillez le renouveler');
            updateStatus(false, 'Token expiré');
            return false;
        }
        return testResponse.ok;
    } catch (error) {
        console.warn('⚠️ Vérification du token échouée:', error);
        return false;
    }
}

// Surveillance périodique du token
let tokenCheckInterval;

function startTokenMonitoring() {
    tokenCheckInterval = setInterval(async () => {
        const isValid = await checkTokenValidity();
        if (!isValid && currentToken) {
            showMessage('warning', '🔄 Token expiré, veuillez le renouveler');
        }
    }, 5 * 60 * 1000); // 5 minutes
}

function stopTokenMonitoring() {
    if (tokenCheckInterval) {
        clearInterval(tokenCheckInterval);
        tokenCheckInterval = null;
    }
}

// ===============================================
// CHARGEMENT DES ÉQUIPES
// ===============================================

async function loadTeams() {
    const token = tokenInput.value.trim();
    if (!token) {
        showMessage('error', 'Token obligatoire');
        return;
    }

    currentToken = token;
    saveToken(token);
    connectBtn.innerHTML = '<div class="loading"></div> Connexion...';
    connectBtn.disabled = true;

    try {
        console.log('🔑 Début de la connexion...');
        const startTime = performance.now();
        
        // Récupérer les clubs avec walletAddress depuis le JWT
        const walletAddress = "0x919957b917ea62d2";
        const clubsResponse = await fetchWithAuth(`/clubs?walletAddress=${walletAddress}`);
        
        if (!clubsResponse.ok) {
            throw new Error(`Erreur ${clubsResponse.status}: Impossible de récupérer vos clubs`);
        }

        const clubs = await clubsResponse.json();
        console.log(`✅ ${clubs.length} club(s) trouvé(s)`);
        
        // PARALLÉLISER : Traiter tous les clubs EN MÊME TEMPS
        const clubPromises = clubs.map(async (clubData) => {
            const club = clubData.club;
            console.log(`🏢 Traitement du club: ${club.name}`);
            
            try {
                const clubDetailsResponse = await fetchWithAuth(`/clubs/${club.id}`);
                
                if (!clubDetailsResponse.ok) {
                    console.warn(`⚠️ Club ${club.name} inaccessible`);
                    return [];
                }
                
                const clubDetails = await clubDetailsResponse.json();
                
                if (!clubDetails.squads || clubDetails.squads.length === 0) {
                    console.warn(`⚠️ Pas d'équipe pour ${club.name}`);
                    return [];
                }
                
                // PARALLÉLISER : Traiter toutes les formations de ce club EN MÊME TEMPS
                const formationPromises = clubDetails.squads.map(async (squad) => {
                    const formation = await loadFormationForSquad(club.id, squad.id);
                    
                    return {
                        clubId: club.id,
                        clubName: club.name,
                        clubLogo: `https://d13e14gtps4iwl.cloudfront.net/u/clubs/${club.id}/logo.png?v=4`,
                        division: clubDetails.division || 'ZZZZZ',
                        squadId: squad.id,
                        squadName: club.name,
                        squadType: squad.type,
                        formation: formation
                    };
                });
                
                // Attendre toutes les formations de ce club
                return await Promise.all(formationPromises);
                
            } catch (error) {
                console.error(`❌ Erreur pour le club ${club.name}:`, error);
                return [];
            }
        });
        
        // Attendre tous les clubs ET toutes leurs formations
        const clubResults = await Promise.all(clubPromises);
        
        // Aplatir les résultats (array of arrays → flat array)
        const allTeams = clubResults.flat();

        if (allTeams.length === 0) {
            throw new Error('Aucune équipe trouvée ou accessible');
        }

        // TRI PAR DIVISION
        allTeams.sort((a, b) => {
        // Convertir les divisions en nombres pour le tri (Diamant = 1, Spark = 9)
        const divisionA = parseInt(a.division) || 999;
        const divisionB = parseInt(b.division) || 999;
            
            if (divisionA !== divisionB) {
                return divisionA - divisionB; // Tri croissant : Diamant (1) en premier
            }
            return a.clubName.localeCompare(b.clubName);
        });

        // ASSIGNER userTeams AVANT loadAdvancedData
        userTeams = allTeams;
        
        // PARALLÉLISER les données avancées aussi
        await loadAdvancedDataOptimized();
        
        displayTeams();
        
        const endTime = performance.now();
        const loadTime = ((endTime - startTime) / 1000).toFixed(1);
        
        updateStatus(true, `${allTeams.length} équipe(s) chargée(s)`);
        showMessage('success', `🎉 ${allTeams.length} équipe(s) trouvée(s) dans ${clubs.length} club(s) (${loadTime}s)`);
        
        startTokenMonitoring();

    } catch (error) {
        console.error('❌ Erreur lors de la connexion:', error);
        updateStatus(false, 'Échec de la connexion');
        showMessage('error', `Erreur: ${error.message}`);
        
        stopTokenMonitoring();
        connectBtn.textContent = 'Se connecter';
        connectBtn.disabled = false;
    }
}

// Version optimisée de loadAdvancedData
async function loadAdvancedDataOptimized() {
    console.log('📊 Chargement des données avancées (parallélisé)...');
    
    // Grouper les équipes par club pour éviter les doublons
    const clubIds = [...new Set(userTeams.map(team => team.clubId))];
    
    // PARALLÉLISER : Charger tous les joueurs de tous les clubs EN MÊME TEMPS
    const playersPromises = clubIds.map(async (clubId) => {
        if (!allClubPlayers[clubId]) {
            return loadClubPlayers(clubId);
        }
    });
    
    // PARALLÉLISER : Charger tous les préréglages de toutes les équipes EN MÊME TEMPS
    const formationsPromises = userTeams.map(team => loadNamedFormations(team));
    
    // Attendre TOUT en parallèle
    await Promise.all([
        ...playersPromises,
        ...formationsPromises
    ]);
    
    console.log('✅ Données avancées chargées en parallèle');
}

// ===============================================
// CHARGEMENT DES DONNÉES AVANCÉES
// ===============================================

async function loadAdvancedData() {
    console.log('📊 Chargement des données avancées...');
    
    for (const team of userTeams) {
        try {
            // Charger les préréglages de formations nommées
            await loadNamedFormations(team);
            
            // Charger les joueurs du club (une seule fois par club)
            if (!allClubPlayers[team.clubId]) {
                await loadClubPlayers(team.clubId);
            }
        } catch (error) {
            console.warn(`⚠️ Erreur données avancées pour ${team.squadName}:`, error);
        }
    }
}

// Charger les formations nommées d'une équipe
async function loadNamedFormations(team) {
    try {
        const response = await fetchWithAuth(`/clubs/${team.clubId}/squads/${team.squadId}/formations/named`);
        
        if (response.ok) {
            const formations = await response.json();
            namedFormations[`${team.clubId}-${team.squadId}`] = formations;
            console.log(`✅ ${formations.length} préréglage(s) chargé(s) pour ${team.squadName}`);
        }
    } catch (error) {
        console.warn(`⚠️ Préréglages inaccessibles pour ${team.squadName}`);
        namedFormations[`${team.clubId}-${team.squadId}`] = [];
    }
}

// Charger tous les joueurs d'un club
async function loadClubPlayers(clubId) {
    try {
        const response = await fetchWithAuth(`/clubs/${clubId}/players`);
        
        if (response.ok) {
            const players = await response.json();
            allClubPlayers[clubId] = players;
            console.log(`✅ ${players.length} joueur(s) chargé(s) pour le club ${clubId}`);
        }
    } catch (error) {
        console.warn(`⚠️ Joueurs inaccessibles pour le club ${clubId}`);
        allClubPlayers[clubId] = [];
    }
}

// ===============================================
// SCRIPT.JS - PARTIE 2A: AFFICHAGE ET INTERFACE
// ===============================================

// ===============================================
// AFFICHAGE DES ÉQUIPES
// ===============================================

function displayTeams() {
    console.log('🎨 Affichage des équipes en accordéon...');
    teamsAccordion.innerHTML = '';

    userTeams.forEach((team, index) => {
        const teamCard = createTeamCard(team, index);
        teamsAccordion.appendChild(teamCard);
    });
    
    console.log('✅ Toutes les équipes affichées');
}

// Créer une carte d'équipe pour l'accordéon

function createTeamCard(team, index) {
    const formation = team.formation;
    const teamStats = calculateTeamStats(team);
    
    const teamCard = document.createElement('div');
    teamCard.className = 'team-card';
    teamCard.innerHTML = `
        <div class="team-header">
            <div class="team-info" onclick="toggleTeamCard(${index})">
                <div class="team-name">
                    <img src="${team.clubLogo}" alt="${team.clubName}" class="club-logo" onerror="this.style.display='none'">
                    ${team.squadName}
                </div>
                <div class="team-division">${getDivisionName(team.division)}</div>
                <div class="team-stats">
                    <span>⚡ ${teamStats.avgEnergy}%</span>
                    <span class="suspension-indicator ${teamStats.suspensions > 0 ? 'has-suspensions' : ''}">🟥 ${teamStats.suspensions}</span>
                    <span>📋 ${formation ? formation.type || 'N/A' : 'N/A'}</span>
                    <span>👥 ${teamStats.playerCount} joueurs</span>
                </div>
            </div>
            
            <!-- BOUTON DE SAUVEGARDE CENTRAL DANS LE HEADER -->
            <div class="team-save-section">
                <button class="action-btn save-header" onclick="saveFormation(${index})" title="Sauvegarder les modifications de cette équipe">
                    💾 Sauvegarder
                </button>
            </div>
            
            <div class="team-quick-actions">
                <div class="preset-buttons-quick">
                    ${generateQuickPresetButtons(team, index)}
                </div>
                <div class="rotation-buttons-quick">
                    <button class="rotation-btn-quick fatigue-btn-quick" onclick="rotateTeamFatigue(${index})" title="Remplacer joueurs fatigués">
                        ⚡
                    </button>
                    <button class="rotation-btn-quick cards-btn-quick" onclick="rotateTeamCards(${index})" title="Remplacer joueurs cartonnés">
                        🟥
                    </button>
                    <button class="rotation-btn-quick auto-btn-quick" onclick="rotateTeamAll(${index})" title="Rotation complète">
                        🚀
                    </button>
                </div>
            </div>
            
            <div class="expand-icon" onclick="toggleTeamCard(${index})">▼</div>
        </div>
        
        <div class="team-content">
            <div class="team-tactics">
                <h4 style="margin-bottom: 1rem; color: #2c3e50;">👥 Formation sur le Terrain</h4>
                <div class="pitch-container">
                    ${formation ? generatePitchView(formation, index, team.clubId) : '<p>Formation non disponible</p>'}
                </div>
                
                <div class="tactics-section">
                    <div class="tactics-header" onclick="toggleTacticsSection(${index})">
                        <h4>⚙️ Réglages Tactiques</h4>
                        <div class="tactics-expand-icon">▼</div>
                    </div>
                    <div class="tactics-content">
                        <div class="tactics-grid">
                            ${formation ? generateTacticsGrid(formation, index) : '<p>Formation non disponible</p>'}
                        </div>
                        <!-- SUPPRESSION DE L'ANCIEN BOUTON DE SAUVEGARDE ICI -->
                    </div>
                </div>
            </div>
        </div>
    `;
    
    return teamCard;
}

// FONCTION POUR TOGGLE LE SOUS-ACCORDÉON TACTIQUES
function toggleTacticsSection(teamIndex) {
    const teamCard = teamsAccordion.children[teamIndex];
    const tacticsSection = teamCard.querySelector('.tactics-section');
    tacticsSection.classList.toggle('expanded');
}

// FONCTION DE VALIDATION DU SEUIL DE FATIGUE
function applyFatigueThreshold() {
    const threshold = parseInt(fatigueThreshold.value);
    if (threshold < 0 || threshold > 100) {
        showMessage('error', 'Le seuil doit être entre 0 et 100%');
        return;
    }
    
    showMessage('success', `✅ Seuil de fatigue mis à jour: ${threshold}%`);
    console.log(`🎯 Nouveau seuil de fatigue global: ${threshold}%`);
}

function isPlayerSuspended(player) {
    // Vérifier les suspensions actives uniquement
    if (player.matchesSuspensions && player.matchesSuspensions.length > 0) {
        const now = Date.now();
        return player.matchesSuspensions.some(suspension => {
            // Si minMatchDate existe et est dans le futur, le joueur est suspendu
            return suspension.minMatchDate && suspension.minMatchDate > now;
        });
    }
    
    return false;
}


// Calculer les statistiques d'une équipe

function calculateTeamStats(team) {
    const clubPlayers = allClubPlayers[team.clubId] || [];
    const formation = team.formation;
    
    let stats = {
        avgEnergy: 0,
        suspensions: 0, // Nouveau : compteur de suspensions actives
        playerCount: 0
    };
    
    if (formation && formation.positions && clubPlayers.length > 0) {
        let totalEnergy = 0;
        let validPlayers = 0;
        
        formation.positions.forEach(position => {
            const player = clubPlayers.find(p => p.id === position.playerId);
            if (player) {
                totalEnergy += player.energy || 0;
                
                // Compter les suspensions actives
                if (isPlayerSuspended(player)) {
                    stats.suspensions++;
                }
                
                validPlayers++;
            }
        });
        
        stats.avgEnergy = validPlayers > 0 ? Math.round(totalEnergy / validPlayers / 100) : 0;
        stats.playerCount = validPlayers;
    }
    
    return stats;
}

// Générer les boutons de préréglages rapides
function generateQuickPresetButtons(team, teamIndex) {
    const teamKey = `${team.clubId}-${team.squadId}`;
    const formations = namedFormations[teamKey] || [];
    
    if (formations.length === 0) {
        return '';
    }
    
    return formations.slice(0, 5).map(formation => 
        `<button class="preset-btn-quick" onclick="applyPresetToTeam(${teamIndex}, '${formation.name}')" title="${formation.name}">
            🎮
        </button>`
    ).join('');
}

// Mapping des positions par formation basé sur l'index
const FORMATION_POSITIONS = {
    '4-4-2_B': [
        { x: 50, y: 85 },  // 0: GK
        { x: 15, y: 65 },  // 1: LB
        { x: 35, y: 70 },  // 2: CB
        { x: 65, y: 70 },  // 3: CB
        { x: 85, y: 65 },  // 4: RB
        { x: 20, y: 45 },  // 5: LM
        { x: 40, y: 40 },  // 6: CM
        { x: 60, y: 40 },  // 7: CM
        { x: 80, y: 45 },  // 8: RM
        { x: 35, y: 15 },  // 9: ST
        { x: 65, y: 15 }   // 10: ST
    ],
    '4-4-2': [
        { x: 50, y: 85 },  // 0: GK
        { x: 15, y: 65 },  // 1: LB
        { x: 35, y: 70 },  // 2: CB
        { x: 65, y: 70 },  // 3: CB
        { x: 85, y: 65 },  // 4: RB
        { x: 20, y: 45 },  // 5: LM
        { x: 40, y: 40 },  // 6: CM
        { x: 60, y: 40 },  // 7: CM
        { x: 80, y: 45 },  // 8: RM
        { x: 35, y: 15 },  // 9: ST
        { x: 65, y: 15 }   // 10: ST
    ],
    '4-1-2-1-2_narrow': [
        { x: 50, y: 85 },  // 0: GK
        { x: 15, y: 65 },  // 1: LB
        { x: 35, y: 70 },  // 2: CB
        { x: 65, y: 70 },  // 3: CB
        { x: 85, y: 65 },  // 4: RB
        { x: 50, y: 55 },  // 5: CDM
        { x: 35, y: 40 },  // 6: CM
        { x: 65, y: 40 },  // 7: CM
        { x: 50, y: 25 },  // 8: CAM
        { x: 35, y: 10 },  // 9: ST
        { x: 65, y: 10 }   // 10: ST
    ],
    '4-2-4': [
        { x: 50, y: 85 },  // 0: GK
        { x: 15, y: 65 },  // 1: LB
        { x: 35, y: 70 },  // 2: CB
        { x: 65, y: 70 },  // 3: CB
        { x: 85, y: 65 },  // 4: RB
        { x: 35, y: 45 },  // 5: CM
        { x: 65, y: 45 },  // 6: CM
        { x: 20, y: 15 },  // 7: LW
        { x: 40, y: 10 },  // 8: ST
        { x: 60, y: 10 },  // 9: ST
        { x: 80, y: 15 }   // 10: RW
    ],
    '4-3-3_attack': [
        { x: 50, y: 85 },  // 0: GK
        { x: 15, y: 65 },  // 1: LB
        { x: 35, y: 70 },  // 2: CB
        { x: 65, y: 70 },  // 3: CB
        { x: 85, y: 65 },  // 4: RB
        { x: 30, y: 45 },  // 5: CM
        { x: 50, y: 40 },  // 6: CM
        { x: 70, y: 45 },  // 7: CM
        { x: 20, y: 15 },  // 8: LW
        { x: 50, y: 10 },  // 9: ST
        { x: 80, y: 15 }   // 10: RW
    ],
    '3-4-3_diamond': [
        { x: 50, y: 85 },  // 0: GK
        { x: 25, y: 65 },  // 1: CB
        { x: 50, y: 70 },  // 2: CB
        { x: 75, y: 65 },  // 3: CB
        { x: 15, y: 40 },  // 4: LM
        { x: 35, y: 45 },  // 5: CM
        { x: 65, y: 45 },  // 6: CM
        { x: 85, y: 40 },  // 7: RM
        { x: 25, y: 15 },  // 8: LW
        { x: 50, y: 10 },  // 9: ST
        { x: 75, y: 15 }   // 10: RW
    ],
    '4-3-2-1': [
        { x: 50, y: 85 },  // 0: GK
        { x: 15, y: 65 },  // 1: LB
        { x: 35, y: 70 },  // 2: CB
        { x: 65, y: 70 },  // 3: CB
        { x: 85, y: 65 },  // 4: RB
        { x: 30, y: 45 },  // 5: CM
        { x: 50, y: 40 },  // 6: CM
        { x: 70, y: 45 },  // 7: CM
        { x: 35, y: 25 },  // 8: CAM
        { x: 65, y: 25 },  // 9: CAM
        { x: 50, y: 10 }   // 10: ST
    ],
    '3-5-2_B': [
        { x: 50, y: 85 },  // 0: GK
        { x: 25, y: 65 },  // 1: CB
        { x: 50, y: 70 },  // 2: CB
        { x: 75, y: 65 },  // 3: CB
        { x: 15, y: 40 },  // 4: LM
        { x: 35, y: 45 },  // 5: CM
        { x: 50, y: 40 },  // 6: CM
        { x: 65, y: 45 },  // 7: CM
        { x: 85, y: 40 },  // 8: RM
        { x: 40, y: 15 },  // 9: ST
        { x: 60, y: 15 }   // 10: ST
    ],
    '3-4-2-1': [
        { x: 50, y: 85 },  // 0: GK
        { x: 25, y: 65 },  // 1: CB
        { x: 50, y: 70 },  // 2: CB
        { x: 75, y: 65 },  // 3: CB
        { x: 20, y: 40 },  // 4: LM
        { x: 40, y: 45 },  // 5: CM
        { x: 60, y: 45 },  // 6: CM
        { x: 80, y: 40 },  // 7: RM
        { x: 35, y: 25 },  // 8: CAM
        { x: 65, y: 25 },  // 9: CAM
        { x: 50, y: 10 }   // 10: ST
    ],
    '5-3-2': [
        { x: 50, y: 85 },  // 0: GK
        { x: 10, y: 60 },  // 1: LWB
        { x: 30, y: 70 },  // 2: CB
        { x: 50, y: 72 },  // 3: CB
        { x: 70, y: 70 },  // 4: CB
        { x: 90, y: 60 },  // 5: RWB
        { x: 30, y: 40 },  // 6: CM
        { x: 50, y: 35 },  // 7: CM
        { x: 70, y: 40 },  // 8: CM
        { x: 40, y: 15 },  // 9: ST
        { x: 60, y: 15 }   // 10: ST
    ],
    '4-2-2-2': [
        { x: 50, y: 85 },  // 0: GK
        { x: 15, y: 65 },  // 1: LB
        { x: 35, y: 70 },  // 2: CB
        { x: 65, y: 70 },  // 3: CB
        { x: 85, y: 65 },  // 4: RB
        { x: 35, y: 45 },  // 5: CM
        { x: 65, y: 45 },  // 6: CM
        { x: 35, y: 25 },  // 7: CAM
        { x: 65, y: 25 },  // 8: CAM
        { x: 35, y: 10 },  // 9: ST
        { x: 65, y: 10 }   // 10: ST
    ]
};

function generatePitchView(formation, teamIndex, clubId) {
    const clubPlayers = allClubPlayers[clubId] || [];
    
    if (!formation.positions || formation.positions.length === 0) {
        return '<p style="text-align: center; color: #666;">Aucun joueur sélectionné</p>';
    }
    
    console.log(`🔍 Debug formation ${teamIndex}:`);
    console.log(`   Type: ${formation.type}`);
    console.log(`   Positions count: ${formation.positions?.length || 0}`);
    console.log(`   Sample position structure:`, formation.positions?.[0]);
    
    // Obtenir les coordonnées prédéfinies pour cette formation
    const formationCoords = FORMATION_POSITIONS[formation.type] || FORMATION_POSITIONS['4-4-2'];
    
    return `
        <div class="pitch-field" id="pitch-${teamIndex}">
            <div class="pitch-positions">
                ${formation.positions.map((position, posIndex) => {
                    const player = clubPlayers.find(p => p.id === position.playerId);
                    if (!player) return '';
                    
                    const cardUrl = `https://d13e14gtps4iwl.cloudfront.net/players/${player.id}/card_512.png?co=${player.metadata.overall}`;
                    const isSuspended = isPlayerSuspended(player);
                    
                    // Utiliser les coordonnées prédéfinies basées sur l'index
                    const positionIndex = position.index || posIndex;
                    const coords = formationCoords[positionIndex] || { x: 50, y: 50 };
                    
                    const xPercent = coords.x;
                    const yPercent = coords.y;
                    
                    console.log(`⚽ ${player.metadata.firstName}: Index ${positionIndex} → CSS(${xPercent.toFixed(1)}%, ${yPercent.toFixed(1)}%)`);
                    
                    return `
                        <div class="player-card-container ${isSuspended ? 'suspended-player' : ''}" 
                             style="left: ${xPercent.toFixed(1)}%; top: ${yPercent.toFixed(1)}%;"
                             data-position="${posIndex}"
                             title="${player.metadata.firstName} ${player.metadata.lastName} - ${player.metadata.positions[0]} (${player.metadata.overall})${isSuspended ? ' - SUSPENDU' : ''}">
                            <img src="${cardUrl}" 
                                 alt="${player.metadata.firstName} ${player.metadata.lastName}"
                                 class="player-card ${isSuspended ? 'suspended-card' : ''}"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iOTAiIGZpbGw9IiNkZGQiIHZpZXdCb3g9IjAgMCA2NCA5MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNjQiIGhlaWdodD0iOTAiIGZpbGw9IiNmOGY5ZmEiIHJ4PSI0Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzY2NiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPj88L3RleHQ+PC9zdmc+'"
                                 style="width: 50px; height: 70px;">
                            ${isSuspended ? '<div class="suspension-overlay">⛔</div>' : ''}
                            <div class="player-info">
                                <div class="player-name">${player.metadata.firstName} ${player.metadata.lastName}</div>
                                <div class="player-position">${player.metadata.positions[0]} (${player.metadata.overall})</div>
                                ${isSuspended ? '<div class="suspension-text">SUSPENDU</div>' : ''}
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        </div>
    `;
}

// ===============================================
// CONTRÔLES TACTIQUES
// ===============================================

function generateTacticsGrid(formation, teamIndex) {
    const tactics = [
        { key: 'pressing', name: 'Pressing' },
        { key: 'width', name: 'Largeur' },
        { key: 'depth', name: 'Profondeur' },
        { key: 'compactness', name: 'Compacité' },
        { key: 'directness', name: 'Direction' },
        { key: 'crosses', name: 'Centres' },
        { key: 'dribble', name: 'Dribbles' },
        { key: 'aggressivity', name: 'Agressivité' }
    ];
    
    return tactics.map(tactic => {
        const rule = TACTIC_RULES[tactic.key];
        const value = formation[tactic.key];
        
        if (!rule) return '';
        
        return `
            <div class="tactic-item">
                <div class="tactic-label">${tactic.name}</div>
                ${rule.type === 'slider' ? 
                    renderSlider(tactic.key, value, teamIndex, rule) : 
                    renderButtons3(tactic.key, value, teamIndex, rule)
                }
            </div>
        `;
    }).join('');
}

// Rendu d'un slider continu
function renderSlider(key, value, teamIndex, rule) {
    const displayValue = value !== null ? value.toFixed(1) : '0.0';
    
    return `
        <div class="tactic-slider">
            <div class="slider-value">${displayValue}</div>
            <div class="slider-controls">
                <button class="slider-btn" onclick="adjustSlider(${teamIndex}, '${key}', -${rule.step})" title="Diminuer">◀</button>
                <input type="range" 
                       min="${rule.min}" 
                       max="${rule.max}" 
                       step="${rule.step}"
                       value="${value || rule.min}"
                       class="slider-input"
                       onchange="updateSlider(${teamIndex}, '${key}', this.value)">
                <button class="slider-btn" onclick="adjustSlider(${teamIndex}, '${key}', ${rule.step})" title="Augmenter">▶</button>
            </div>
        </div>
    `;
}

// Rendu des boutons 3 états
function renderButtons3(key, value, teamIndex, rule) {
    let currentIndex = rule.values.findIndex(v => v === value);
    if (currentIndex === -1) currentIndex = 1; // Par défaut équilibré (null)
    
    const displayValue = rule.display[currentIndex];
    
    return `
        <div class="tactic-buttons3">
            <div class="buttons3-display">${displayValue}</div>
            <div class="buttons3-controls">
                ${rule.labels.map((label, i) => `
                    <button class="btn3 ${i === currentIndex ? 'active' : ''}" 
                            onclick="setButtons3(${teamIndex}, '${key}', ${i})"
                            title="${label}">
                        ${rule.display[i]}
                    </button>
                `).join('')}
            </div>
        </div>
    `;
}

// ===============================================
// INTERACTIONS UTILISATEUR DE BASE
// ===============================================

function toggleTeamCard(index) {
    const teamCard = teamsAccordion.children[index];
    teamCard.classList.toggle('expanded');
}

function adjustSlider(teamIndex, tacticKey, adjustment) {
    const team = userTeams[teamIndex];
    if (!team.formation) return;
    
    const rule = TACTIC_RULES[tacticKey];
    const currentValue = team.formation[tacticKey] || rule.min;
    const newValue = Math.max(rule.min, Math.min(rule.max, currentValue + adjustment));
    
    team.formation[tacticKey] = Math.round(newValue * 10) / 10; // Arrondir à 1 décimale
    
    // Mettre à jour l'affichage
    updateTeamCard(teamIndex);
    
    console.log(`🎚️ ${team.squadName}: ${tacticKey} = ${team.formation[tacticKey]}`);
}

// Mettre à jour un slider
function updateSlider(teamIndex, tacticKey, value) {
    const team = userTeams[teamIndex];
    if (!team.formation) return;
    
    team.formation[tacticKey] = parseFloat(value);
    
    // Mettre à jour l'affichage
    updateTeamCard(teamIndex);
    
    console.log(`🎚️ ${team.squadName}: ${tacticKey} = ${team.formation[tacticKey]}`);
}

// Définir la valeur des boutons 3 états
function setButtons3(teamIndex, tacticKey, valueIndex) {
    const team = userTeams[teamIndex];
    if (!team.formation) return;
    
    const rule = TACTIC_RULES[tacticKey];
    team.formation[tacticKey] = rule.values[valueIndex];
    
    // Mettre à jour l'affichage
    updateTeamCard(teamIndex);
    
    console.log(`🔘 ${team.squadName}: ${tacticKey} = ${team.formation[tacticKey]} (${rule.labels[valueIndex]})`);
}

// Mettre à jour une carte d'équipe
function updateTeamCard(teamIndex) {
    const team = userTeams[teamIndex];
    const newCard = createTeamCard(team, teamIndex);
    const oldCard = teamsAccordion.children[teamIndex];
    
    // Conserver l'état d'expansion
    const wasExpanded = oldCard.classList.contains('expanded');
    teamsAccordion.replaceChild(newCard, oldCard);
    
    if (wasExpanded) {
        newCard.classList.add('expanded');
    }
}
// ===============================================
// SCRIPT.JS - PARTIE 2B: SAUVEGARDE ET ACTIONS
// ===============================================

// ===============================================
// SAUVEGARDE DES FORMATIONS
// ===============================================

async function saveFormation(teamIndex) {
    const team = userTeams[teamIndex];
    if (!team.formation) return;
    
    // CIBLER LE NOUVEAU BOUTON DANS LE HEADER
    const saveBtn = teamsAccordion.children[teamIndex].querySelector('.save-header');
    const originalText = saveBtn.textContent;
    saveBtn.innerHTML = '<div class="loading"></div>';
    saveBtn.disabled = true;

    try {
        console.log(`💾 Sauvegarde de ${team.squadName}...`);
        
        // STRUCTURE PARFAITE - OMETTRE les champs null au lieu de les envoyer
        const formationToSave = {
            // OBLIGATOIRE EN PREMIER
            "formationType": team.formation.type,
            
            // TACTIQUES SLIDERS (toujours présentes)
            "compactness": team.formation.compactness !== null ? team.formation.compactness : 0.65,
            "depth": team.formation.depth !== null ? team.formation.depth : 1.35,
            "width": team.formation.width !== null ? team.formation.width : 1.0,
            
            // POSITIONS ET LISTES
            "positions": team.formation.positions,
            "cornerLeftPlayers": team.formation.cornerLeftPlayers || [],
            "cornerRightPlayers": team.formation.cornerRightPlayers || [],
            "penaltyPlayers": team.formation.penaltyPlayers || [],
            "freeKickDirectPlayers": team.formation.freeKickDirectPlayers || [],
            "freeKickIndirectPlayers": team.formation.freeKickIndirectPlayers || [],
            "pitchType": team.formation.pitchType || "EC24_FRANCE",
            "playersInstructions": team.formation.playersInstructions || {}
        };
        
        // AJOUTER SEULEMENT les tactiques NON-NULL (mode équilibré = omis complètement)
        if (team.formation.crosses !== null) formationToSave.crosses = team.formation.crosses;
        if (team.formation.directness !== null) formationToSave.directness = team.formation.directness;
        if (team.formation.dribble !== null) formationToSave.dribble = team.formation.dribble;
        if (team.formation.farShot !== null) formationToSave.farShot = team.formation.farShot;
        if (team.formation.pressing !== null) formationToSave.pressing = team.formation.pressing;
        if (team.formation.sideAttackLeft !== null) formationToSave.sideAttackLeft = team.formation.sideAttackLeft;
        if (team.formation.aggressivity !== null) formationToSave.aggressivity = team.formation.aggressivity;
        if (team.formation.clearance !== null) formationToSave.clearance = team.formation.clearance;

        console.log('📋 Formation à sauvegarder:', formationToSave);

        const response = await fetchWithAuth(`/clubs/${team.clubId}/squads/${team.squadId}/formation`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formationToSave)
        });

        if (response.ok) {
            console.log('🎉 Formation sauvegardée avec succès!');
            
            try {
                const responseData = await response.json();
                console.log('📋 Nouvelle formation ID:', responseData.id);
                
                // Mettre à jour la formation locale
                team.formation = responseData;
                
                // Actualiser l'affichage
                updateTeamCard(teamIndex);
                
            } catch {
                console.log('ℹ️ Sauvegarde confirmée (pas de données de retour)');
            }
            
            showMessage('success', `🎉 Formation sauvegardée: ${team.squadName}`);
            
        } else {
            const errorText = await response.text();
            console.error('❌ Échec de la sauvegarde:', errorText);
            
            let errorMessage = 'Erreur lors de la sauvegarde';
            try {
                const errorData = JSON.parse(errorText);
                if (errorData.message) {
                    errorMessage = errorData.message;
                }
            } catch {
                errorMessage = 'Erreur de format de réponse';
            }
            
            showMessage('error', `❌ ${team.squadName}: ${errorMessage}`);
        }
        
    } catch (error) {
        console.error('❌ Erreur lors de la sauvegarde:', error);
        showMessage('error', `💥 Erreur technique: ${error.message}`);
    } finally {
        saveBtn.textContent = originalText;
        saveBtn.disabled = false;
    }
}

// ===============================================
// GESTION DES PRÉRÉGLAGES
// ===============================================

// Appliquer un préréglage à une équipe spécifique
async function applyPresetToTeam(teamIndex, presetName) {
    const team = userTeams[teamIndex];
    const button = event.target;
    
    button.disabled = true;
    button.textContent = '⏳';
    
    try {
        const teamKey = `${team.clubId}-${team.squadId}`;
        const formations = namedFormations[teamKey] || [];
        const preset = formations.find(f => f.name === presetName);
        
        if (preset) {
            const success = await applyFormationToTeam(team, preset);
            
            if (success) {
                showMessage('success', `🎉 Préréglage "${presetName}" appliqué à ${team.squadName}`);
                
                // Recharger la formation et mettre à jour l'affichage
                team.formation = await loadFormationForSquad(team.clubId, team.squadId);
                setTimeout(() => {
                    updateTeamCard(teamIndex);
                }, 1000);
            } else {
                showMessage('error', `❌ Erreur lors de l'application du préréglage`);
            }
        }
    } catch (error) {
        console.error(`❌ Erreur préréglage:`, error);
        showMessage('error', `❌ Erreur technique: ${error.message}`);
    } finally {
        button.disabled = false;
        button.textContent = '🎮';
    }
}

// Appliquer une formation à une équipe
async function applyFormationToTeam(team, preset) {
    try {
        const formationData = {
            formationType: preset.type,
            positions: preset.positions,
            depth: preset.depth,
            compactness: preset.compactness,
            width: preset.width,
            cornerLeftPlayers: preset.cornerLeftPlayers || [],
            cornerRightPlayers: preset.cornerRightPlayers || [],
            penaltyPlayers: preset.penaltyPlayers || [],
            freeKickDirectPlayers: preset.freeKickDirectPlayers || [],
            freeKickIndirectPlayers: preset.freeKickIndirectPlayers || [],
            pitchType: preset.pitchType || "EC24_FRANCE",
            playersInstructions: preset.playersInstructions || {}
        };
        
        // Ajouter les tactiques non-null
        if (preset.pressing !== null) formationData.pressing = preset.pressing;
        if (preset.directness !== null) formationData.directness = preset.directness;
        if (preset.crosses !== null) formationData.crosses = preset.crosses;
        if (preset.dribble !== null) formationData.dribble = preset.dribble;
        if (preset.farShot !== null) formationData.farShot = preset.farShot;
        if (preset.sideAttackLeft !== null) formationData.sideAttackLeft = preset.sideAttackLeft;
        if (preset.aggressivity !== null) formationData.aggressivity = preset.aggressivity;
        if (preset.clearance !== null) formationData.clearance = preset.clearance;
        
        const response = await fetchWithAuth(`/clubs/${team.clubId}/squads/${team.squadId}/formation`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formationData)
        });
        
        return response.ok;
    } catch (error) {
        console.error(`❌ Erreur application formation:`, error);
        return false;
    }
}

// ===============================================
// SYSTÈME DE ROTATIONS
// ===============================================

// Rotation d'une équipe - Fatigués
async function rotateTeamFatigue(teamIndex) {
    await rotateSpecificTeam(teamIndex, 'fatigue');
}

// Rotation d'une équipe - Cartonnés
async function rotateTeamCards(teamIndex) {
    await rotateSpecificTeam(teamIndex, 'cards');
}

// Rotation d'une équipe - Complète
async function rotateTeamAll(teamIndex) {
    await rotateSpecificTeam(teamIndex, 'all');
}

// Rotation spécifique à une équipe
async function rotateSpecificTeam(teamIndex, type) {
    const team = userTeams[teamIndex];
    const threshold = parseInt(fatigueThreshold.value);
    const maxEnergy = 10000;
    const minEnergyRequired = (threshold / 100) * maxEnergy;
    
    let rotationCount = 0;
    
    try {
        const clubPlayers = allClubPlayers[team.clubId] || [];
        
        if (team.formation && team.formation.positions) {
            const newPositions = [...team.formation.positions];
            let needsUpdate = false;
            
            for (let i = 0; i < newPositions.length; i++) {
                const position = newPositions[i];
                const currentPlayer = clubPlayers.find(p => p.id === position.playerId);
                
                let shouldReplace = false;
                
                if (currentPlayer) {
                    if (type === 'fatigue' || type === 'all') {
                        shouldReplace = currentPlayer.energy < minEnergyRequired;
                    }
                    if ((type === 'cards' || type === 'all') && !shouldReplace) {shouldReplace = isPlayerSuspended(currentPlayer);
                    }
                    
                    if (shouldReplace) {
                        const replacement = findReplacement(
                            currentPlayer, 
                            clubPlayers, 
                            type.includes('fatigue') ? minEnergyRequired : 0, 
                            team.formation.positions, 
                            type.includes('cards')
                        );
                        
                        if (replacement) {
                            newPositions[i] = { ...position, playerId: replacement.id };
                            needsUpdate = true;
                            rotationCount++;
                        }
                    }
                }
            }
            
            if (needsUpdate) {
                await updateFormationPositions(team, newPositions);
                updateTeamCard(teamIndex);
            }
        }
    } catch (error) {
        console.error(`❌ Erreur rotation ${team.squadName}:`, error);
    }
    
    if (rotationCount > 0) {
        const typeText = type === 'fatigue' ? 'fatigués' : type === 'cards' ? 'cartonnés' : 'fatigués/cartonnés';
        showMessage('success', `✅ ${rotationCount} joueur(s) ${typeText} remplacé(s) dans ${team.squadName}`);
    } else {
        showMessage('info', `ℹ️ Aucun joueur à remplacer dans ${team.squadName}`);
    }
}

// Trouver un remplaçant
function findReplacement(currentPlayer, allPlayers, minEnergy = 0, currentPositions = [], avoidCards = false) {
    const currentPositionIds = currentPositions.map(p => p.playerId);
    
    const candidates = allPlayers.filter(player => {
        if (player.id === currentPlayer.id) return false;
        if (currentPositionIds.includes(player.id)) return false;
        if (player.energy < minEnergy) return false;
        if (avoidCards && isPlayerSuspended(player)) return false;
        
        const currentPlayerPositions = currentPlayer.metadata.positions || [];
        const candidatePositions = player.metadata.positions || [];
        
        return currentPlayerPositions.some(pos => candidatePositions.includes(pos));
    });
    
    candidates.sort((a, b) => {
        if (b.energy !== a.energy) return b.energy - a.energy;
        return b.metadata.overall - a.metadata.overall;
    });
    
    return candidates[0] || null;
}

// Mettre à jour les positions d'une formation
async function updateFormationPositions(team, newPositions) {
    try {
        const formationToSave = {
            formationType: team.formation.type,
            positions: newPositions,
            depth: team.formation.depth !== null ? team.formation.depth : 1.35,
            compactness: team.formation.compactness !== null ? team.formation.compactness : 0.65,
            width: team.formation.width !== null ? team.formation.width : 1.0,
            cornerLeftPlayers: team.formation.cornerLeftPlayers || [],
            cornerRightPlayers: team.formation.cornerRightPlayers || [],
            penaltyPlayers: team.formation.penaltyPlayers || [],
            freeKickDirectPlayers: team.formation.freeKickDirectPlayers || [],
            freeKickIndirectPlayers: team.formation.freeKickIndirectPlayers || [],
            pitchType: team.formation.pitchType || "EC24_FRANCE",
            playersInstructions: team.formation.playersInstructions || {}
        };
        
        // Ajouter les tactiques non-null
        if (team.formation.crosses !== null) formationToSave.crosses = team.formation.crosses;
        if (team.formation.directness !== null) formationToSave.directness = team.formation.directness;
        if (team.formation.dribble !== null) formationToSave.dribble = team.formation.dribble;
        if (team.formation.farShot !== null) formationToSave.farShot = team.formation.farShot;
        if (team.formation.pressing !== null) formationToSave.pressing = team.formation.pressing;
        if (team.formation.sideAttackLeft !== null) formationToSave.sideAttackLeft = team.formation.sideAttackLeft;
        if (team.formation.aggressivity !== null) formationToSave.aggressivity = team.formation.aggressivity;
        if (team.formation.clearance !== null) formationToSave.clearance = team.formation.clearance;
        
        const response = await fetchWithAuth(`/clubs/${team.clubId}/squads/${team.squadId}/formation`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formationToSave)
        });
        
        if (response.ok) {
            const updatedFormation = await response.json();
            team.formation = updatedFormation;
            return true;
        }
        
        return false;
    } catch (error) {
        console.error(`❌ Erreur mise à jour formation:`, error);
        return false;
    }
}

