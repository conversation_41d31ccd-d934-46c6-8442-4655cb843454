{"version": 3, "file": "event-options.js", "sourceRoot": "", "sources": ["../../src/decorators/event-options.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAUH,OAAO,EAAC,gBAAgB,EAAC,MAAM,WAAW,CAAC;AAE3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,MAAM,UAAU,YAAY,CAAC,OAAgC;IAC3D,OAAO,gBAAgB,CAAC;QACtB,QAAQ,EAAE,CAAC,IAA4B,EAAE,IAAiB,EAAE,EAAE;YAC5D,MAAM,CAAC,MAAM;YACX,8DAA8D;YAC9D,IAAI,CAAC,SAAS,CAAC,IAA6B,CAAQ,EACpD,OAAO,CACR,CAAC;QACJ,CAAC;KACF,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\n\nimport {ReactiveElement} from '../reactive-element.js';\nimport {decorateProperty} from './base.js';\n\n/**\n * Adds event listener options to a method used as an event listener in a\n * lit-html template.\n *\n * @param options An object that specifies event listener options as accepted by\n * `EventTarget#addEventListener` and `EventTarget#removeEventListener`.\n *\n * Current browsers support the `capture`, `passive`, and `once` options. See:\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Parameters\n *\n * ```ts\n * class MyElement {\n *   clicked = false;\n *\n *   render() {\n *     return html`\n *       <div @click=${this._onClick}>\n *         <button></button>\n *       </div>\n *     `;\n *   }\n *\n *   @eventOptions({capture: true})\n *   _onClick(e) {\n *     this.clicked = true;\n *   }\n * }\n * ```\n * @category Decorator\n */\nexport function eventOptions(options: AddEventListenerOptions) {\n  return decorateProperty({\n    finisher: (ctor: typeof ReactiveElement, name: PropertyKey) => {\n      Object.assign(\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        ctor.prototype[name as keyof ReactiveElement] as any,\n        options\n      );\n    },\n  });\n}\n"]}