// ===============================================
// GESTIONNAIRE D'INTERFACE UTILISATEUR - PlayMFL Manager
// PARTIE 1/2 - Base, Messages, Statut, Affichage des équipes
// ===============================================

const UIManager = {
    
    // Éléments DOM cachés
    elements: {},
    
    // ===============================================
    // INITIALISATION
    // ===============================================
    
    init() {
        this.elements = {
            tokenInput: document.getElementById('tokenInput'),
            connectBtn: document.getElementById('connectBtn'),
            statusDot: document.getElementById('statusDot'),
            statusText: document.getElementById('statusText'),
            messageArea: document.getElementById('messageArea'),
            teamsContainer: document.getElementById('teamsContainer'),
            teamsAccordion: document.getElementById('teamsAccordion'),
            globalControls: document.getElementById('globalControls'),
            fatigueThreshold: document.getElementById('fatigueThreshold'),
            teamsStats: document.getElementById('teamsStats'),
            totalTeams: document.getElementById('totalTeams'),
            totalPlayers: document.getElementById('totalPlayers'),
            avgRating: document.getElementById('avgRating'),
            loadingOverlay: document.getElementById('loadingOverlay')
        };

        // Ajouter les styles CSS pour les ratings s'ils n'existent pas
        this.addRatingStyles();
    },

    // ===============================================
    // AJOUT DES STYLES CSS POUR LES RATINGS
    // ===============================================
    
    addRatingStyles() {
        // Vérifier si les styles existent déjà
        if (document.getElementById('rating-styles')) return;

        const style = document.createElement('style');
        style.id = 'rating-styles';
        style.textContent = `
            /* Styles pour les badges de rating des joueurs */
            .player-rating-badge {
                position: absolute;
                top: -8px;
                right: -8px;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                font-weight: bold;
                font-family: 'Orbitron', monospace;
                color: white;
                text-shadow: 0 1px 2px rgba(0,0,0,0.8);
                border: 2px solid white;
                box-shadow: 0 2px 6px rgba(0,0,0,0.4);
                z-index: 15;
                animation: ratingBadgeGlow 2s ease-in-out infinite alternate;
            }

            @keyframes ratingBadgeGlow {
                0% { transform: scale(1); }
                100% { transform: scale(1.05); }
            }

            .rating-excellent {
                background: linear-gradient(135deg, #2ecc71, #27ae60);
                box-shadow: 0 0 15px rgba(46, 204, 113, 0.6);
            }

            .rating-good {
                background: linear-gradient(135deg, #3498db, #2980b9);
                box-shadow: 0 0 15px rgba(52, 152, 219, 0.6);
            }

            .rating-average {
                background: linear-gradient(135deg, #f39c12, #e67e22);
                box-shadow: 0 0 15px rgba(243, 156, 18, 0.6);
            }

            .rating-poor {
                background: linear-gradient(135deg, #e74c3c, #c0392b);
                box-shadow: 0 0 15px rgba(231, 76, 60, 0.6);
            }

            /* Hover effect pour les badges */
            .player-card-container:hover .player-rating-badge {
                transform: scale(1.2);
                z-index: 20;
            }

            /* Responsive pour les petits écrans */
            @media (max-width: 800px) {
                .player-rating-badge {
                    width: 20px;
                    height: 20px;
                    font-size: 8px;
                    top: -6px;
                    right: -6px;
                }
            }

            @media (max-width: 480px) {
                .player-rating-badge {
                    width: 18px;
                    height: 18px;
                    font-size: 7px;
                    top: -5px;
                    right: -5px;
                }
            }
        `;
        
        document.head.appendChild(style);
        console.log('✅ Styles CSS pour les ratings ajoutés');
    },

    // ===============================================
    // GESTION DES MESSAGES
    // ===============================================
    
    showMessage(type, message) {
        if (!this.elements.messageArea) {
            console.log(`Message ${type}:`, message);
            return;
        }
        
        this.elements.messageArea.innerHTML = `<div class="message ${type}">${message}</div>`;
        
        // Temps d'affichage selon le type
        const timeouts = CONFIG.MESSAGE_TIMEOUTS;
        const timeout = timeouts[type] || timeouts.success;
        
        setTimeout(() => {
            if (this.elements.messageArea) {
                this.elements.messageArea.innerHTML = '';
            }
        }, timeout);
    },

    // ===============================================
    // GESTION DU STATUT
    // ===============================================
    
    updateStatus(connected, message) {
        if (this.elements.statusDot) {
            this.elements.statusDot.className = `status-dot ${connected ? 'connected' : ''}`;
        }
        if (this.elements.statusText) {
            this.elements.statusText.textContent = message;
        }
        if (this.elements.connectBtn) {
            this.elements.connectBtn.textContent = connected ? 'Connecté ✓' : 'Se connecter';
            this.elements.connectBtn.disabled = connected;
        }
        if (this.elements.teamsContainer) {
            this.elements.teamsContainer.className = `teams-container ${connected ? 'show' : ''}`;
        }
        if (this.elements.globalControls) {
            this.elements.globalControls.style.display = connected ? 'block' : 'none';
        }
    },

    // ===============================================
    // AFFICHAGE DES ÉQUIPES
    // ===============================================
    
    displayTeams(teams) {
        console.log('🎨 Affichage des équipes en accordéon...');
        
        if (!this.elements.teamsAccordion) {
            console.error('❌ Élément teamsAccordion introuvable');
            return;
        }
        
        this.elements.teamsAccordion.innerHTML = '';

        teams.forEach((team, index) => {
            const teamCard = this.createTeamCard(team, index);
            this.elements.teamsAccordion.appendChild(teamCard);
        });
        
        // Mettre à jour les statistiques
        this.updateTeamsStats(teams);
        
        console.log('✅ Toutes les équipes affichées');
    },

    // ===============================================
    // CRÉATION D'UNE CARTE D'ÉQUIPE
    // ===============================================
    
    createTeamCard(team, index) {
        const formation = team.formation;
        const clubPlayers = TeamManager.getClubPlayers(team.clubId);
        const teamStats = PlayerSystem.calculateTeamStats(team, clubPlayers);
        
        const teamCard = document.createElement('div');
        teamCard.className = 'team-card';
        teamCard.innerHTML = `
            <div class="team-header">
                <div class="team-info" onclick="toggleTeamCard(${index})">
                    <div class="team-name">
                        <img src="${team.clubLogo}" alt="${team.clubName}" class="club-logo" onerror="this.style.display='none'">
                        ${team.squadName}
                    </div>
                    <div class="team-division">${App.getDivisionName(team.division)}</div>
                    <div class="team-stats">
                        <span>⚡ ${teamStats.avgEnergy}%</span>
                        <span class="suspension-indicator ${teamStats.suspensions > 0 ? 'has-suspensions' : ''}">🟥 ${teamStats.suspensions}</span>
                        <span>📋 ${formation ? formation.type || 'N/A' : 'N/A'}</span>
                        <span>👥 ${teamStats.playerCount} joueurs</span>
                        <span class="team-rating">⭐ ${teamStats.avgRating}</span>
                    </div>
                </div>
                
                <!-- Bouton de sauvegarde central -->
                <div class="team-save-section">
                    <button class="action-btn save-header" onclick="saveFormation(${index})" title="Sauvegarder les modifications de cette équipe">
                        💾 Sauvegarder
                    </button>
                </div>
                
                <div class="team-quick-actions">
                    <div class="preset-buttons-quick">
                        ${this.generateQuickPresetButtons(team, index)}
                    </div>
                    <div class="rotation-buttons-quick">
                        <button class="rotation-btn-quick smart-rotation-btn-quick" onclick="smartRotateTeam(${index})" title="Rotation intelligente complète (remplacements + échanges optimaux)">
                            🔄 Rotation
                        </button>
                        <button class="rotation-btn-quick test-btn-quick" onclick="testRotationComplete(${index})" title="Test complet du système de rotation">
                            🧪 Test
                        </button>
                    </div>
                </div>
                
                <div class="expand-icon" onclick="toggleTeamCard(${index})">▼</div>
            </div>
            
            <div class="team-content">
                <div class="team-tactics">
                    <h4 style="margin-bottom: 1rem; color: var(--orange-glow);">👥 Formation sur le Terrain</h4>
                    <div class="pitch-container">
                        ${formation ? this.generatePitchView(formation, index, team.clubId) : '<p>Formation non disponible</p>'}
                    </div>
                    
                    <div class="tactics-section">
                        <div class="tactics-header" onclick="toggleTacticsSection(${index})">
                            <h4>⚙️ Réglages Tactiques</h4>
                            <div class="tactics-expand-icon">▼</div>
                        </div>
                        <div class="tactics-content">
                            <div class="tactics-grid">
                                ${formation ? this.generateTacticsGrid(formation, index) : '<p>Formation non disponible</p>'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        return teamCard;
    },

    // ===============================================
    // GÉNÉRATION DU TERRAIN - VERSION CORRIGÉE
    // ===============================================
    
    generatePitchView(formation, teamIndex, clubId) {
        const clubPlayers = TeamManager.getClubPlayers(clubId);
        
        if (!formation.positions || formation.positions.length === 0) {
            return '<p style="text-align: center; color: #666;">Aucun joueur sélectionné</p>';
        }
        
        // Obtenir les coordonnées prédéfinies
        const formationCoords = this.getFormationCoordinates(formation.type);
        
        return `
            <div class="pitch-field" id="pitch-${teamIndex}">
                <div class="pitch-positions">
                    ${formation.positions.map((position, posIndex) => {
                        const player = clubPlayers.find(p => p.id === position.playerId);
                        if (!player) return '';
                        
                        const cardUrl = App.getPlayerCardUrl(player.id, player.metadata.overall);
                        const isSuspended = PlayerSystem.isPlayerSuspended(player);
                        
                        const positionIndex = position.index || posIndex;
                        const coords = formationCoords[positionIndex] || { x: 50, y: 50 };
                        
                        // Calculer le rating pour cette position avec le mapping précis
                        const expectedPositions = getFormationMapping(formation.type);
                        const expectedPosition = expectedPositions[positionIndex] || 'CM';
                        const rating = PlayerSystem.calculatePositionRating(player, expectedPosition);
                        const ratingClass = PlayerSystem.getPlayerRatingClass(rating);
                        
                        return `
                            <div class="player-card-container ${isSuspended ? 'suspended-player' : ''}" 
                                 style="left: ${coords.x.toFixed(1)}%; top: ${coords.y.toFixed(1)}%;"
                                 data-position="${posIndex}"
                                 data-suspended="${isSuspended}"
                                 title="${player.metadata.firstName} ${player.metadata.lastName}
Position: ${expectedPosition} | Rating: ${rating}
Familiarité: ${PlayerSystem.getPositionFamiliarity(player, expectedPosition)}
Énergie: ${Math.round((player.energy / 10000) * 100)}%
Positions naturelles: [${player.metadata.positions?.join(', ') || 'Aucune'}]
Overall: ${player.metadata.overall}${isSuspended ? ' - 🚫 SUSPENDU' : ''}">
                                <img src="${cardUrl}" 
                                     alt="${player.metadata.firstName} ${player.metadata.lastName}"
                                     class="player-card"
                                     onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iOTAiIGZpbGw9IiNkZGQiIHZpZXdCb3g9IjAgMCA2NCA5MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNjQiIGhlaWdodD0iOTAiIGZpbGw9IiNmOGY5ZmEiIHJ4PSI0Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzY2NiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPj88L3RleHQ+PC9zdmc+'"
                                     style="width: 50px; height: 70px;">
                                ${isSuspended ? '<div class="suspension-overlay">🚫</div>' : ''}
                                <div class="player-rating-badge ${ratingClass}">${rating}</div>
                                <div class="player-info">
                                    <div class="player-name">${player.metadata.firstName} ${player.metadata.lastName}</div>
                                    <div class="player-position">${player.metadata.positions[0]} (${player.metadata.overall})</div>
                                    ${isSuspended ? '<div class="suspension-text">SUSPENDU</div>' : ''}
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    },

    // ===============================================
    // GÉNÉRATION DES PRÉRÉGLAGES
    // ===============================================
    
    generateQuickPresetButtons(team, teamIndex) {
        const formations = TeamManager.getNamedFormations(team.clubId, team.squadId);
        
        if (formations.length === 0) {
            return '';
        }
        
        return formations.slice(0, 5).map(formation => 
            `<button class="preset-btn-quick" onclick="applyPresetToTeam(${teamIndex}, '${formation.name}')" title="${formation.name}">
                🎮
            </button>`
        ).join('');
    },

    // ===============================================
    // UTILITAIRES D'AFFICHAGE
    // ===============================================
    
    updateTeamCard(teamIndex) {
        const team = TeamManager.getTeam(teamIndex);
        const newCard = this.createTeamCard(team, teamIndex);
        const oldCard = this.elements.teamsAccordion.children[teamIndex];
        
        // Conserver l'état d'expansion
        const wasExpanded = oldCard?.classList.contains('expanded');
        const wasTacticsExpanded = oldCard?.querySelector('.tactics-section.expanded');
        
        if (oldCard) {
            this.elements.teamsAccordion.replaceChild(newCard, oldCard);
            
            if (wasExpanded) {
                newCard.classList.add('expanded');
            }
            if (wasTacticsExpanded) {
                const tacticsSection = newCard.querySelector('.tactics-section');
                if (tacticsSection) {
                    tacticsSection.classList.add('expanded');
                }
            }
        }
    },

    updateTeamsStats(teams) {
        if (!this.elements.teamsStats) return;
        
        let totalPlayers = 0;
        let totalRating = 0;
        let ratingCount = 0;
        
        teams.forEach(team => {
            const clubPlayers = TeamManager.getClubPlayers(team.clubId);
            const stats = PlayerSystem.calculateTeamStats(team, clubPlayers);
            totalPlayers += stats.playerCount;
            if (stats.avgRating > 0) {
                totalRating += stats.avgRating;
                ratingCount++;
            }
        });
        
        const avgRating = ratingCount > 0 ? Math.round(totalRating / ratingCount) : 0;
        
        if (this.elements.totalTeams) {
            this.elements.totalTeams.textContent = `${teams.length} équipe(s)`;
        }
        if (this.elements.totalPlayers) {
            this.elements.totalPlayers.textContent = `${totalPlayers} joueur(s)`;
        }
        if (this.elements.avgRating) {
            this.elements.avgRating.textContent = `Note moyenne: ${avgRating}`;
        }
        if (this.elements.teamsStats) {
            this.elements.teamsStats.style.display = 'block';
        }
    }
};

console.log('✅ Gestionnaire d\'interface utilisateur chargé (Partie 1/2)');

// ===============================================
// GESTIONNAIRE D'INTERFACE UTILISATEUR - PlayMFL Manager
// PARTIE 2/2 - Tactiques, Contrôles et Coordonnées des formations
// ===============================================

// Extension de UIManager avec les méthodes restantes
Object.assign(UIManager, {

    // ===============================================
    // GÉNÉRATION DES CONTRÔLES TACTIQUES
    // ===============================================
    
    generateTacticsGrid(formation, teamIndex) {
        const tactics = [
            { key: 'pressing', name: 'Pressing' },
            { key: 'width', name: 'Largeur' },
            { key: 'depth', name: 'Profondeur' },
            { key: 'compactness', name: 'Compacité' },
            { key: 'directness', name: 'Direction' },
            { key: 'crosses', name: 'Centres' },
            { key: 'dribble', name: 'Dribbles' },
            { key: 'aggressivity', name: 'Agressivité' },
            { key: 'tempo', name: 'Tempo' },
            { key: 'clearance', name: 'Dégagement' },
            { key: 'farShot', name: 'Tirs loin' },
            { key: 'tackles', name: 'Tacles' }
        ];
        
        return tactics.map(tactic => {
            const rule = TACTIC_RULES[tactic.key];
            const value = formation[tactic.key];
            
            if (!rule) return '';
            
            return `
                <div class="tactic-item">
                    <div class="tactic-label">${tactic.name}</div>
                    ${rule.type === 'slider' ? 
                        this.renderSlider(tactic.key, value, teamIndex, rule) : 
                        this.renderButtons3(tactic.key, value, teamIndex, rule)
                    }
                </div>
            `;
        }).join('');
    },

    // ===============================================
    // HELPERS POUR LES CONTRÔLES
    // ===============================================
    
    renderSlider(key, value, teamIndex, rule) {
        const displayValue = value !== null && value !== undefined ? value.toFixed(1) : '0.0';
        
        return `
            <div class="tactic-slider">
                <div class="slider-value">${displayValue}</div>
                <div class="slider-controls">
                    <button class="slider-btn" onclick="adjustSlider(${teamIndex}, '${key}', -${rule.step})" title="Diminuer">◀</button>
                    <input type="range" 
                           min="${rule.min}" 
                           max="${rule.max}" 
                           step="${rule.step}"
                           value="${value !== null && value !== undefined ? value : rule.min}"
                           class="slider-input"
                           onchange="updateSlider(${teamIndex}, '${key}', this.value)">
                    <button class="slider-btn" onclick="adjustSlider(${teamIndex}, '${key}', ${rule.step})" title="Augmenter">▶</button>
                </div>
            </div>
        `;
    },

    renderButtons3(key, value, teamIndex, rule) {
        let currentIndex = rule.values.findIndex(v => v === value);
        if (currentIndex === -1) currentIndex = 1; // Par défaut équilibré
        
        const displayValue = rule.display[currentIndex];
        
        return `
            <div class="tactic-buttons3">
                <div class="buttons3-display">${displayValue}</div>
                <div class="buttons3-controls">
                    ${rule.labels.map((label, i) => `
                        <button class="btn3 ${i === currentIndex ? 'active' : ''}" 
                                onclick="setButtons3(${teamIndex}, '${key}', ${i})"
                                title="${label}">
                            ${rule.display[i]}
                        </button>
                    `).join('')}
                </div>
            </div>
        `;
    },

    // ===============================================
    // COORDONNÉES DES FORMATIONS - VERSION COMPLÈTE
    // ===============================================
    
    getFormationCoordinates(formationType) {
        // Utiliser le mapping précis des formations pour positionner correctement
        const expectedPositions = getFormationMapping(formationType);

        const FORMATION_POSITIONS = {
            // Formation 4-4-2 classique
            '4-4-2': [
                { x: 50, y: 85 }, // 0: GK
                { x: 15, y: 65 }, // 1: LB
                { x: 35, y: 70 }, // 2: CB
                { x: 65, y: 70 }, // 3: CB
                { x: 85, y: 65 }, // 4: RB
                { x: 20, y: 45 }, // 5: LM
                { x: 40, y: 40 }, // 6: CM
                { x: 60, y: 40 }, // 7: CM
                { x: 80, y: 45 }, // 8: RM
                { x: 35, y: 15 }, // 9: ST
                { x: 65, y: 15 }  // 10: ST
            ],

            '4-4-2_B': [
                { x: 50, y: 85 }, { x: 15, y: 65 }, { x: 35, y: 70 }, { x: 65, y: 70 }, { x: 85, y: 65 },
                { x: 20, y: 45 }, { x: 40, y: 40 }, { x: 60, y: 40 }, { x: 80, y: 45 },
                { x: 35, y: 15 }, { x: 65, y: 15 }
            ],

            '4-3-3_attack': [
                { x: 50, y: 85 }, { x: 15, y: 65 }, { x: 35, y: 70 }, { x: 65, y: 70 }, { x: 85, y: 65 },
                { x: 30, y: 45 }, { x: 50, y: 40 }, { x: 70, y: 45 },
                { x: 20, y: 15 }, { x: 50, y: 10 }, { x: 80, y: 15 }
            ],

            '4-1-2-1-2_narrow': [
                { x: 50, y: 85 }, // 0: GK
                { x: 15, y: 65 }, // 1: RB → CORRIGÉ (était à droite)
                { x: 35, y: 70 }, // 2: CB (correct)
                { x: 65, y: 70 }, // 3: CB (correct)
                { x: 85, y: 65 }, // 4: LB → CORRIGÉ (était à gauche)
                { x: 50, y: 55 }, // 5: CDM (correct)
                { x: 35, y: 45 }, // 6: CM (correct)
                { x: 65, y: 45 }, // 7: CM (correct)
                { x: 40, y: 15 }, // 8: ST (correct)
                { x: 50, y: 25 }, // 9: CAM (correct)
                { x: 60, y: 15 }  // 10: ST (correct)
            ],

            '4-2-4': [
                { x: 50, y: 85 }, { x: 15, y: 65 }, { x: 35, y: 70 }, { x: 65, y: 70 }, { x: 85, y: 65 },
                { x: 35, y: 50 }, { x: 65, y: 50 }, { x: 20, y: 15 }, { x: 40, y: 10 }, { x: 60, y: 10 }, { x: 80, y: 15 }
            ],

            '4-3-2-1': [
                { x: 50, y: 85 }, { x: 15, y: 65 }, { x: 35, y: 70 }, { x: 65, y: 70 }, { x: 85, y: 65 },
                { x: 30, y: 45 }, { x: 50, y: 40 }, { x: 70, y: 45 },
                { x: 40, y: 25 }, { x: 60, y: 25 }, { x: 50, y: 10 }
            ],

            '3-5-2_B': [
                { x: 50, y: 85 }, { x: 25, y: 70 }, { x: 50, y: 75 }, { x: 75, y: 70 },
                { x: 10, y: 50 }, { x: 35, y: 45 }, { x: 50, y: 40 }, { x: 65, y: 45 }, { x: 90, y: 50 },
                { x: 40, y: 15 }, { x: 60, y: 15 }
            ],

            '3-4-3_diamond': [
                { x: 50, y: 85 }, { x: 25, y: 70 }, { x: 50, y: 75 }, { x: 75, y: 70 },
                { x: 20, y: 45 }, { x: 40, y: 40 }, { x: 60, y: 40 }, { x: 80, y: 45 },
                { x: 20, y: 15 }, { x: 50, y: 10 }, { x: 80, y: 15 }
            ],

            '3-4-2-1': [
                { x: 50, y: 85 }, { x: 25, y: 70 }, { x: 50, y: 75 }, { x: 75, y: 70 },
                { x: 20, y: 45 }, { x: 40, y: 40 }, { x: 60, y: 40 }, { x: 80, y: 45 },
                { x: 35, y: 25 }, { x: 65, y: 25 }, { x: 50, y: 10 }
            ],

            '5-3-2': [
                { x: 50, y: 85 }, // GK
                { x: 10, y: 65 }, // LWB
                { x: 25, y: 70 }, // CB gauche
                { x: 50, y: 75 }, // CB centre
                { x: 75, y: 70 }, // CB droit
                { x: 90, y: 65 }, // RWB
                { x: 35, y: 45 }, // CM gauche
                { x: 50, y: 40 }, // CM centre
                { x: 65, y: 45 }, // CM droit
                { x: 40, y: 15 }, // ST gauche
                { x: 60, y: 15 }  // ST droit
            ],

            '4-2-2-2': [
                { x: 50, y: 85 }, { x: 15, y: 65 }, { x: 35, y: 70 }, { x: 65, y: 70 }, { x: 85, y: 65 },
                { x: 35, y: 50 }, { x: 65, y: 50 }, { x: 35, y: 30 }, { x: 65, y: 30 },
                { x: 40, y: 10 }, { x: 60, y: 10 }
            ],

            // Formations supplémentaires
            '3-5-2': [
                { x: 50, y: 85 }, { x: 25, y: 70 }, { x: 50, y: 75 }, { x: 75, y: 70 },
                { x: 10, y: 50 }, { x: 35, y: 45 }, { x: 50, y: 40 }, { x: 65, y: 45 }, { x: 90, y: 50 },
                { x: 40, y: 15 }, { x: 60, y: 15 }
            ],

            '4-5-1': [
                { x: 50, y: 85 }, { x: 15, y: 65 }, { x: 35, y: 70 }, { x: 65, y: 70 }, { x: 85, y: 65 },
                { x: 20, y: 50 }, { x: 35, y: 45 }, { x: 50, y: 40 }, { x: 65, y: 45 }, { x: 80, y: 50 },
                { x: 50, y: 15 }
            ],

            '4-1-4-1': [
                { x: 50, y: 85 }, { x: 15, y: 65 }, { x: 35, y: 70 }, { x: 65, y: 70 }, { x: 85, y: 65 },
                { x: 50, y: 55 }, { x: 20, y: 40 }, { x: 40, y: 35 }, { x: 60, y: 35 }, { x: 80, y: 40 },
                { x: 50, y: 15 }
            ],

            '5-4-1': [
                { x: 50, y: 85 }, // 0: GK
                { x: 90, y: 65 }, // 1: LWB → CORRIGÉ (était à droite)
                { x: 70, y: 70 }, // 2: CB → CORRIGÉ (échangé avec 4)
                { x: 50, y: 75 }, // 3: CB (central, correct)
                { x: 30, y: 70 }, // 4: CB → CORRIGÉ (échangé avec 2)
                { x: 10, y: 65 }, // 5: RWB → CORRIGÉ (était à gauche)
                { x: 25, y: 45 }, // 6: RM → CORRIGÉ (était LM)
                { x: 50, y: 50 }, // 7: CDM (correct)
                { x: 75, y: 45 }, // 8: LM → CORRIGÉ (était RM)
                { x: 50, y: 30 }, // 9: CAM (cor
                { x: 50, y: 15 }  // 10: ST 
            ]
        };
        
        // Retourner les coordonnées ou une formation par défaut
        return FORMATION_POSITIONS[formationType] || FORMATION_POSITIONS['4-4-2'];
    }
});

console.log('✅ Gestionnaire d\'interface utilisateur chargé (Partie 2/2)');