<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PlayMFL Manager - Gestionnaire d'Équipes Intelligent</title>
    <meta name="description" content="Gestionnaire intelligent d'équipes PlayMFL avec Dapper Wallet">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    
    <!-- FCL pour Dapper Wallet - Chargement direct -->
    <script src="https://unpkg.com/@onflow/fcl@1.4.1/dist/fcl.js"></script>
    
    <style>
        /* Styles pour la section d'authentification */
        .auth-section {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
            min-width: 300px;
            max-width: 500px;
            flex: 1;
        }

        .auth-separator {
            width: 100%;
            text-align: center;
            position: relative;
            margin: 0.5rem 0;
        }

        .auth-separator::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--border-color);
        }

        .auth-separator span {
            background: var(--secondary-bg);
            padding: 0 1rem;
            color: var(--text-muted);
            font-size: 0.8rem;
            position: relative;
            z-index: 1;
        }
        
        /* Styles pour le bouton Dapper */
        .dapper-btn.connect-btn {
            background: linear-gradient(135deg, #ff6b35, #e55a2b);
            color: white;
            border: 2px solid #ff6b35;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Orbitron', monospace;
            font-size: 0.9rem;
            font-weight: 700;
            transition: all 0.3s ease;
            text-transform: uppercase;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            width: 100%;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .dapper-btn.connect-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.5);
        }

        .dapper-btn.connect-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }
    </style>
</head>
<body>
    <!-- Header avec Dapper Wallet -->
    <header class="header">
        <div class="logo">
            <img src="mfl-logo.png" alt="PlayMFL Manager" onerror="this.style.display='none'">
        </div>
        
        <!-- Section d'authentification -->
        <div class="auth-section">
            <!-- Bouton Dapper Wallet -->
            <button id="dapperConnectBtn" class="dapper-btn connect-btn">
                🔥 Connexion avec Dapper Wallet
            </button>
            
            <!-- Séparateur -->
            <div class="auth-separator">
                <span>ou</span>
            </div>
            
            <!-- Authentification manuelle -->
            <div class="auth-inputs">
                <input type="password" id="tokenInput" placeholder="Token API manuel" class="token-input">
                <button id="connectBtn" class="connect-btn">🔑 Connexion manuelle</button>
            </div>
        </div>
        
        <!-- Statut -->
        <div class="status">
            <div class="status-dot" id="statusDot"></div>
            <span id="statusText">Déconnecté</span>
        </div>
    </header>

    <div class="container">
        <div id="messageArea" class="message-area"></div>
        
        <div class="global-controls" id="globalControls" style="display: none;">
            <div class="fatigue-setting-global">
                <label for="fatigueThreshold">Seuil de fatigue global :</label>
                <input type="number" id="fatigueThreshold" min="0" max="100" value="94">
                <span class="help-text">% minimum d'énergie requis pour jouer</span>
            </div>
        </div>

        <div class="teams-container" id="teamsContainer">
            <div class="teams-header">
                <h2>👥 Vos Équipes PlayMFL</h2>
                <div class="teams-stats" id="teamsStats" style="display: none;">
                    <span id="totalTeams"></span>
                    <span id="totalPlayers"></span>
                    <span id="avgRating"></span>
                </div>
            </div>
            <div class="teams-accordion" id="teamsAccordion"></div>
        </div>
    </div>

    <!-- Vos modules PlayMFL -->
    <script src="js/config/constants.js"></script>
    <script src="js/core/storage.js"></script>
    <script src="js/core/api.js"></script>
    <script src="js/features/players.js"></script>
    <script src="js/features/rotations.js"></script>
    <script src="js/ui/interface.js"></script>
    <script src="js/global-functions.js"></script>
    <script src="js/app.js"></script>
    
    <!-- Script minimal Dapper Wallet -->
    <script>
        // Configuration et initialisation de Dapper Wallet
        document.addEventListener('DOMContentLoaded', function() {
            console.log("🚀 Initialisation Dapper Wallet...");
            
            // Vérifier que FCL est chargé
            if (typeof fcl === 'undefined') {
                console.error("❌ FCL non chargé - Connexion Dapper non disponible");
                const dapperBtn = document.getElementById('dapperConnectBtn');
                if (dapperBtn) {
                    dapperBtn.innerHTML = '❌ Dapper non disponible';
                    dapperBtn.disabled = true;
                    dapperBtn.title = 'FCL n\'a pas pu être chargé';
                }
                return;
            }
            
            console.log("✅ FCL détecté:", typeof fcl);
            
            // Configuration FCL
            fcl.config()
                .put("accessNode.api", "https://rest-mainnet.onflow.org")
                .put("discovery.wallet", "https://fcl-discovery.onflow.org/authn")
                .put("discovery.authn.include", ["0xead892083b3e2c6c"]) // ID de Dapper sur mainnet
                .put("app.detail.title", "PlayMFL Manager")
                .put("app.detail.icon", window.location.origin + "/mfl-logo.png");
            
            console.log("✅ FCL configuré pour Dapper Wallet");

            // Configurer le bouton Dapper
            const dapperBtn = document.getElementById('dapperConnectBtn');
            if (dapperBtn) {
                dapperBtn.addEventListener('click', handleDapperLogin);
                console.log('✅ Bouton Dapper Wallet initialisé');
            }

            // Gérer les changements d'utilisateur
            fcl.currentUser.subscribe(handleUserChange);

            // Fonction de connexion
            async function handleDapperLogin() {
                try {
                    dapperBtn.innerHTML = '🔄 Connexion...';
                    dapperBtn.disabled = true;
                    
                    console.log('🚀 Tentative de connexion Dapper...');
                    await fcl.authenticate();
                } catch (error) {
                    console.error('❌ Erreur connexion Dapper:', error);
                    dapperBtn.innerHTML = '🔥 Connexion avec Dapper Wallet';
                    dapperBtn.disabled = false;
                    
                    if (window.UIManager) {
                        window.UIManager.showMessage('error', `Erreur Dapper: ${error.message}`);
                    }
                }
            }

            // Gestion des changements d'utilisateur
            function handleUserChange(user) {
                console.log('👤 État utilisateur Dapper:', user);
                
                if (user && user.loggedIn) {
                    console.log('✅ Connecté à Dapper! Adresse:', user.addr);
                    
                    // Mettre à jour le bouton
                    if (dapperBtn) {
                        dapperBtn.innerHTML = `✅ Dapper connecté (${user.addr.slice(0, 6)}...)`;
                        dapperBtn.disabled = true;
                    }
                    
                    // Générer un token pour PlayMFL
                    const timestamp = Date.now();
                    const walletShort = user.addr.slice(2, 10);
                    const playmflToken = `dapper_${walletShort}_${timestamp}`;
                    
                    console.log('🔑 Token PlayMFL généré:', playmflToken);
                    
                    // Remplir le champ token
                    const tokenInput = document.getElementById('tokenInput');
                    if (tokenInput) {
                        tokenInput.value = playmflToken;
                        
                        // Déclencher la connexion PlayMFL
                        const connectBtn = document.getElementById('connectBtn');
                        if (connectBtn) {
                            connectBtn.click();
                        }
                    }
                    
                    if (window.UIManager) {
                        window.UIManager.showMessage('success', 
                            `🎉 Connexion Dapper réussie! (${user.addr.slice(0, 6)}...${user.addr.slice(-4)})`
                        );
                    }
                }
            }
            
            // Fonction de déconnexion de Dapper (à exposer globalement)
            window.logoutDapper = async function() {
                try {
                    await fcl.unauthenticate();
                    console.log("👋 Déconnecté de Dapper Wallet");
                    
                    if (dapperBtn) {
                        dapperBtn.innerHTML = '🔥 Connexion avec Dapper Wallet';
                        dapperBtn.disabled = false;
                    }
                    
                    if (window.UIManager) {
                        window.UIManager.showMessage('info', 'Déconnecté de Dapper Wallet');
                    }
                } catch (error) {
                    console.error("❌ Erreur déconnexion:", error);
                }
            };
            
            // Exposer des outils de debug
            window.dapperDebug = {
                status: () => {
                    console.log("👤 État utilisateur actuel:", fcl.currentUser.snapshot());
                    return fcl.currentUser.snapshot();
                },
                fcl: () => typeof fcl,
                connect: handleDapperLogin,
                disconnect: window.logoutDapper
            };

            // Diagnostic des modules chargés
            window.diagnosticModules = function() {
                const modules = {
                    'CONFIG': typeof CONFIG !== 'undefined',
                    'StorageManager': typeof StorageManager !== 'undefined',
                    'ApiManager': typeof ApiManager !== 'undefined',
                    'UIManager': typeof UIManager !== 'undefined',
                    'App': typeof App !== 'undefined',
                    'FCL': typeof fcl !== 'undefined'
                };

                console.log('📊 État des modules:');
                Object.entries(modules).forEach(([name, loaded]) => {
                    console.log(`${loaded ? '✅' : '❌'} ${name}: ${loaded ? 'Chargé' : 'Non chargé'}`);
                });

                return modules;
            };

            // Test complet du système
            window.testSystem = function() {
                console.log('🧪 Test complet du système PlayMFL Manager...');

                // 1. Test des modules
                const modules = window.diagnosticModules();
                const allModulesLoaded = Object.values(modules).every(loaded => loaded);

                if (!allModulesLoaded) {
                    console.error('❌ Certains modules ne sont pas chargés');
                    return false;
                }

                // 2. Test de l'interface
                const requiredElements = ['tokenInput', 'connectBtn', 'dapperConnectBtn', 'statusText'];
                const elementsOk = requiredElements.every(id => {
                    const element = document.getElementById(id);
                    const exists = !!element;
                    console.log(`${exists ? '✅' : '❌'} Élément ${id}: ${exists ? 'Présent' : 'Manquant'}`);
                    return exists;
                });

                // 3. Test FCL/Dapper
                const fclOk = typeof fcl !== 'undefined' && typeof fcl.config === 'function';
                console.log(`${fclOk ? '✅' : '❌'} FCL/Dapper: ${fclOk ? 'Fonctionnel' : 'Non fonctionnel'}`);

                // 4. Test de la configuration
                const configOk = typeof CONFIG !== 'undefined' && CONFIG.API_BASE_URL;
                console.log(`${configOk ? '✅' : '❌'} Configuration: ${configOk ? 'Chargée' : 'Manquante'}`);

                const allTestsPassed = allModulesLoaded && elementsOk && fclOk && configOk;

                console.log(`\n🎯 Résultat final: ${allTestsPassed ? '✅ TOUS LES TESTS PASSÉS' : '❌ CERTAINS TESTS ONT ÉCHOUÉ'}`);

                if (allTestsPassed) {
                    console.log('🎉 Le site PlayMFL Manager est prêt à être utilisé !');
                    console.log('💡 Pour tester la connexion Dapper, cliquez sur le bouton orange "Connexion avec Dapper Wallet"');
                    console.log('💡 Pour tester la connexion manuelle, entrez un token dans le champ et cliquez sur "Connexion manuelle"');
                }

                return allTestsPassed;
            };
        });
    </script>
</body>
</html>