<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PlayMFL Manager - Gestionnaire d'Équipes Intelligent</title>
    <meta name="description" content="Gestionnaire intelligent d'équipes PlayMFL avec Dapper Wallet">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    

    
    <style>
        /* Styles pour la section d'authentification */
        .auth-section {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
            min-width: 300px;
            max-width: 500px;
            flex: 1;
        }
    </style>
</head>
<body>
    <!-- Header avec Dapper Wallet -->
    <header class="header">
        <div class="logo">
            <img src="mfl-logo.png" alt="PlayMFL Manager" onerror="this.style.display='none'">
        </div>
        
        <!-- Section d'authentification -->
        <div class="auth-section">
            <!-- Authentification manuelle -->
            <div class="auth-inputs">
                <input type="password" id="tokenInput" placeholder="Token API PlayMFL" class="token-input">
                <button id="connectBtn" class="connect-btn">🔑 Se connecter</button>
            </div>
        </div>
        
        <!-- Statut -->
        <div class="status">
            <div class="status-dot" id="statusDot"></div>
            <span id="statusText">Déconnecté</span>
        </div>
    </header>

    <div class="container">
        <div id="messageArea" class="message-area"></div>
        
        <div class="global-controls" id="globalControls" style="display: none;">
            <div class="fatigue-setting-global">
                <label for="fatigueThreshold">Seuil de fatigue global :</label>
                <input type="number" id="fatigueThreshold" min="0" max="100" value="94">
                <span class="help-text">% minimum d'énergie requis pour jouer</span>
            </div>
        </div>

        <div class="teams-container" id="teamsContainer">
            <div class="teams-header">
                <h2>👥 Vos Équipes PlayMFL</h2>
                <div class="teams-stats" id="teamsStats" style="display: none;">
                    <span id="totalTeams"></span>
                    <span id="totalPlayers"></span>
                    <span id="avgRating"></span>
                </div>
            </div>
            <div class="teams-accordion" id="teamsAccordion"></div>
        </div>
    </div>

    <!-- Vos modules PlayMFL -->
    <script src="js/config/constants.js"></script>
    <script src="js/core/storage.js"></script>
    <script src="js/core/api.js"></script>
    <script src="js/features/players.js"></script>
    <script src="js/features/rotations.js"></script>
    <script src="js/ui/interface.js"></script>
    <script src="js/global-functions.js"></script>
    <script src="js/app.js"></script>
    
    <!-- Script de diagnostic simplifié -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log("🚀 Initialisation PlayMFL Manager...");

            // Diagnostic des modules chargés
            window.diagnosticModules = function() {
                const modules = {
                    'CONFIG': typeof CONFIG !== 'undefined',
                    'StorageManager': typeof StorageManager !== 'undefined',
                    'ApiManager': typeof ApiManager !== 'undefined',
                    'UIManager': typeof UIManager !== 'undefined',
                    'App': typeof App !== 'undefined'
                };

                console.log('📊 État des modules:');
                Object.entries(modules).forEach(([name, loaded]) => {
                    console.log(`${loaded ? '✅' : '❌'} ${name}: ${loaded ? 'Chargé' : 'Non chargé'}`);
                });

                return modules;
            };

            // Test complet du système
            window.testSystem = function() {
                console.log('🧪 Test complet du système PlayMFL Manager...');

                // 1. Test des modules
                const modules = window.diagnosticModules();
                const allModulesLoaded = Object.values(modules).every(loaded => loaded);

                if (!allModulesLoaded) {
                    console.error('❌ Certains modules ne sont pas chargés');
                    return false;
                }

                // 2. Test de l'interface
                const requiredElements = ['tokenInput', 'connectBtn', 'statusText'];
                const elementsOk = requiredElements.every(id => {
                    const element = document.getElementById(id);
                    const exists = !!element;
                    console.log(`${exists ? '✅' : '❌'} Élément ${id}: ${exists ? 'Présent' : 'Manquant'}`);
                    return exists;
                });

                // 3. Test de la configuration
                const configOk = typeof CONFIG !== 'undefined' && CONFIG.API_BASE_URL;
                console.log(`${configOk ? '✅' : '❌'} Configuration: ${configOk ? 'Chargée' : 'Manquante'}`);

                const allTestsPassed = allModulesLoaded && elementsOk && configOk;

                console.log(`\n🎯 Résultat final: ${allTestsPassed ? '✅ TOUS LES TESTS PASSÉS' : '❌ CERTAINS TESTS ONT ÉCHOUÉ'}`);

                if (allTestsPassed) {
                    console.log('🎉 Le site PlayMFL Manager est prêt à être utilisé !');
                    console.log('💡 Pour vous connecter, entrez votre token PlayMFL dans le champ et cliquez sur "Se connecter"');
                    console.log('🔧 Fonctions de debug disponibles:');
                    console.log('   - debugPositions(teamIndex, position) : Analyser les candidats pour une position');
                    console.log('   - testRotationLogic(teamIndex) : Tester la logique complète de rotation');
                    console.log('   - testRotationComplete(teamIndex) : Test complet avec simulation');
                    console.log('   - simulateFatigue(teamIndex) : Simuler des joueurs fatigués');
                    console.log('   - simulateCards(teamIndex) : Simuler des joueurs cartonnés');
                    console.log('   - resetSimulation(teamIndex) : Remettre à zéro les simulations');
                }

                return allTestsPassed;
            };

            console.log("✅ PlayMFL Manager initialisé");
        });
    </script>
</body>
</html>