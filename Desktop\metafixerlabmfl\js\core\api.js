// ===============================================
// GESTION API ET AUTHENTIFICATION - PlayMFL Manager
// Version simplifiée - Connexion manuelle uniquement
// ===============================================

const ApiManager = {
    
    // Variables d'état
    currentToken: '',
    tokenCheckInterval: null,
    
    // ===============================================
    // AUTHENTIFICATION
    // ===============================================
    
    async fetchWithAuth(endpoint, options = {}) {
        const defaultHeaders = {
            'Authorization': `Bearer ${this.currentToken}`,
            'Accept': '*/*',
            'Origin': CONFIG.PLAYMFL_BASE_URL,
            'Referer': `${CONFIG.PLAYMFL_BASE_URL}/`
        };

        const url = `${CONFIG.API_BASE_URL}${endpoint}`;
        console.log(`🌐 ${options.method || 'GET'} ${url}`);

        try {
            const response = await fetch(url, {
                ...options,
                headers: { ...defaultHeaders, ...options.headers }
            });

            console.log(`📡 ${response.status} ${response.statusText}`);
            
            // Gérer les erreurs spécifiques
            if (response.status === 400) {
                const errorText = await response.text();
                console.error('❌ Erreur 400 (Bad Request):', errorText);
                throw new Error(`Requête invalide: Vérifiez les données envoyées`);
            }
            
            if (response.status === 403) {
                const errorText = await response.text();
                console.error('❌ Erreur 403 (Forbidden):', errorText);
                throw new Error(`Accès interdit: Permissions insuffisantes`);
            }
            
            if (response.status === 401) {
                throw new Error('Token expiré ou invalide');
            }
            
            return response;
        } catch (error) {
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('Erreur de connexion réseau');
            }
            throw error;
        }
    },

    // ===============================================
    // GESTION DU TOKEN
    // ===============================================
    
    setToken(token) {
        this.currentToken = token;
        StorageManager.saveToken(token);
        
        // Stocker le type de connexion (Dapper ou manuel)
        if (token && token.startsWith('dapper_')) {
            StorageManager.saveData('connectionType', 'dapper');
        } else if (token) {
            StorageManager.saveData('connectionType', 'manual');
        } else {
            StorageManager.removeData('connectionType');
        }
    },

    getToken() {
        return this.currentToken;
    },

    async checkTokenValidity() {
        if (!this.currentToken) return false;
        
        try {
            const testResponse = await this.fetchWithAuth('/clubs?limit=1');
            if (testResponse.status === 401) {
                console.warn('⚠️ Token expiré, veuillez le renouveler');

                // Notifier l'interface si elle est disponible
                if (typeof UIManager !== 'undefined') {
                    UIManager.showMessage('warning', '⚠️ Token expiré, veuillez le renouveler');
                    UIManager.updateStatus(false, 'Token expiré');
                }

                return false;
            }
            return testResponse.ok;
        } catch (error) {
            console.warn('⚠️ Vérification du token échouée:', error);
            return false;
        }
    },

    startTokenMonitoring() {
        this.tokenCheckInterval = setInterval(async () => {
            const isValid = await this.checkTokenValidity();
            if (!isValid && this.currentToken) {
                console.warn('🔄 Token expiré, veuillez le renouveler');
                if (typeof UIManager !== 'undefined') {
                    UIManager.showMessage('warning', '🔄 Token expiré, veuillez le renouveler');
                }
            }
        }, CONFIG.TOKEN_CHECK_INTERVAL);
    },

    stopTokenMonitoring() {
        if (this.tokenCheckInterval) {
            clearInterval(this.tokenCheckInterval);
            this.tokenCheckInterval = null;
        }
    },

    // ===============================================
    // CHARGEMENT DES DONNÉES
    // ===============================================
    
    async loadFormationForSquad(clubId, squadId) {
        try {
            const response = await this.fetchWithAuth(`/clubs/${clubId}/squads/${squadId}/formation`);
            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.warn(`⚠️ Formation inaccessible pour squad ${squadId}:`, error.message);
        }
        return null;
    },

    async loadClubPlayers(clubId) {
        try {
            const response = await this.fetchWithAuth(`/clubs/${clubId}/players`);
            
            if (response.ok) {
                const players = await response.json();
                console.log(`✅ ${players.length} joueur(s) chargé(s) pour le club ${clubId}`);
                return players;
            }
        } catch (error) {
            console.warn(`⚠️ Joueurs inaccessibles pour le club ${clubId}:`, error.message);
        }
        return [];
    },

    async loadNamedFormations(clubId, squadId) {
        try {
            const response = await this.fetchWithAuth(`/clubs/${clubId}/squads/${squadId}/formations/named`);
            
            if (response.ok) {
                const formations = await response.json();
                console.log(`✅ ${formations.length} préréglage(s) chargé(s) pour squad ${squadId}`);
                return formations;
            }
        } catch (error) {
            console.warn(`⚠️ Préréglages inaccessibles pour squad ${squadId}:`, error.message);
        }
        return [];
    },

    // ===============================================
    // CHARGEMENT DES ÉQUIPES
    // ===============================================
    
    async loadTeams(token) {
        this.setToken(token);
        
        try {
            console.log('🔑 Début de la connexion...');
            const startTime = performance.now();
            
            // Récupérer les clubs
            // Note: Modifié pour prendre l'adresse du wallet si disponible pour Dapper
            let walletAddress = "0x919957b917ea62d2"; // Adresse par défaut
            
            // Si token Dapper et FCL disponible, utiliser l'adresse du wallet
            if (token.startsWith('dapper_') && typeof fcl !== 'undefined') {
                const user = fcl.currentUser.snapshot();
                if (user && user.addr) {
                    walletAddress = user.addr;
                    console.log('🔍 Utilisation adresse Dapper Wallet:', walletAddress);
                }
            }
            
            const clubsResponse = await this.fetchWithAuth(`/clubs?walletAddress=${walletAddress}`);
            
            if (!clubsResponse.ok) {
                throw new Error(`Erreur ${clubsResponse.status}: Impossible de récupérer vos clubs`);
            }

            const clubs = await clubsResponse.json();
            console.log(`✅ ${clubs.length} club(s) trouvé(s)`);
            
            // Traiter tous les clubs en parallèle
            const clubPromises = clubs.map(async (clubData) => {
                const club = clubData.club;
                console.log(`🏢 Traitement du club: ${club.name}`);
                
                try {
                    const clubDetailsResponse = await this.fetchWithAuth(`/clubs/${club.id}`);
                    
                    if (!clubDetailsResponse.ok) {
                        console.warn(`⚠️ Club ${club.name} inaccessible`);
                        return [];
                    }
                    
                    const clubDetails = await clubDetailsResponse.json();
                    
                    if (!clubDetails.squads || clubDetails.squads.length === 0) {
                        console.warn(`⚠️ Pas d'équipe pour ${club.name}`);
                        return [];
                    }
                    
                    // Traiter toutes les formations de ce club en parallèle
                    const formationPromises = clubDetails.squads.map(async (squad) => {
                        const formation = await this.loadFormationForSquad(club.id, squad.id);
                        
                        return {
                            clubId: club.id,
                            clubName: club.name,
                            clubLogo: `${CONFIG.CDN_BASE_URL}/u/clubs/${club.id}/logo.png?v=4`,
                            division: clubDetails.division || 'ZZZZZ',
                            squadId: squad.id,
                            squadName: club.name,
                            squadType: squad.type,
                            formation: formation
                        };
                    });
                    
                    return await Promise.all(formationPromises);
                    
                } catch (error) {
                    console.error(`❌ Erreur pour le club ${club.name}:`, error);
                    return [];
                }
            });
            
            const clubResults = await Promise.all(clubPromises);
            const allTeams = clubResults.flat();

            if (allTeams.length === 0) {
                throw new Error('Aucune équipe trouvée ou accessible');
            }

            // Tri par division
            allTeams.sort((a, b) => {
                const divisionA = parseInt(a.division) || 999;
                const divisionB = parseInt(b.division) || 999;
                
                if (divisionA !== divisionB) {
                    return divisionA - divisionB;
                }
                return a.clubName.localeCompare(b.clubName);
            });

            const endTime = performance.now();
            const loadTime = ((endTime - startTime) / 1000).toFixed(1);
            
            console.log(`✅ ${allTeams.length} équipe(s) chargée(s) en ${loadTime}s`);
            
            return {
                teams: allTeams,
                loadTime: loadTime,
                clubCount: clubs.length
            };

        } catch (error) {
            console.error('❌ Erreur lors de la connexion:', error);
            throw error;
        }
    },

    // ===============================================
    // SAUVEGARDE DES FORMATIONS
    // ===============================================
    
    async saveFormation(team) {
        if (!team.formation) return false;
        
        try {
            console.log(`💾 Sauvegarde de ${team.squadName}...`);
            
            // Nettoyer et valider les données avant envoi
            const formationToSave = this.cleanFormationData(team.formation);
            
            console.log('📋 Formation à sauvegarder:', formationToSave);

            const response = await this.fetchWithAuth(`/clubs/${team.clubId}/squads/${team.squadId}/formation`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formationToSave)
            });

            if (response.ok) {
                console.log('🎉 Formation sauvegardée avec succès!');
                
                try {
                    const responseData = await response.json();
                    console.log('📋 Nouvelle formation ID:', responseData.id);
                    return responseData;
                } catch {
                    console.log('ℹ️ Sauvegarde confirmée (pas de données de retour)');
                    return true;
                }
            } else {
                const errorText = await response.text();
                console.error('❌ Échec de la sauvegarde:', errorText);
                throw new Error('Échec de la sauvegarde');
            }
            
        } catch (error) {
            console.error('❌ Erreur lors de la sauvegarde:', error);
            throw error;
        }
    },

    // ===============================================
    // APPLICATION DE PRÉRÉGLAGES
    // ===============================================
    
    async applyFormationToTeam(team, preset) {
        try {
            console.log(`🎮 Application du préréglage "${preset.name}" pour ${team.squadName}`);
            
            // Nettoyer et valider les données du preset
            const formationData = this.cleanFormationData(preset);
            
            const response = await this.fetchWithAuth(`/clubs/${team.clubId}/squads/${team.squadId}/formation`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formationData)
            });
            
            if (response.ok) {
                console.log(`✅ Préréglage "${preset.name}" appliqué avec succès`);
                return true;
            } else {
                const errorText = await response.text();
                console.error(`❌ Erreur application préréglage:`, errorText);
                throw new Error(`Impossible d'appliquer le préréglage`);
            }
        } catch (error) {
            console.error(`❌ Erreur application formation:`, error);
            throw error;
        }
    },

    // ===============================================
    // MISE À JOUR DES POSITIONS
    // ===============================================
    
    async updateFormationPositions(team, newPositions) {
        try {
            console.log(`🔄 Mise à jour des positions pour ${team.squadName}`);
            
            // Vérifier et corriger les doublons AVANT de nettoyer
            const cleanedPositions = this.removeDuplicatePositions(newPositions);
            
            const formationToSave = this.cleanFormationData({
                ...team.formation,
                positions: cleanedPositions
            });
            
            const response = await this.fetchWithAuth(`/clubs/${team.clubId}/squads/${team.squadId}/formation`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(formationToSave)
            });
            
            if (response.ok) {
                const updatedFormation = await response.json();
                console.log(`✅ Positions mises à jour pour ${team.squadName}`);
                return updatedFormation;
            } else {
                const errorText = await response.text();
                console.error(`❌ Erreur mise à jour positions:`, errorText);
                throw new Error('Impossible de mettre à jour les positions');
            }
            
        } catch (error) {
            console.error(`❌ Erreur mise à jour formation:`, error);
            throw error;
        }
    },

    // ===============================================
    // UTILITAIRES
    // ===============================================
    
    removeDuplicatePositions(positions) {
        const usedPlayerIds = new Set();
        const cleanPositions = [];
        
        positions.forEach((position, index) => {
            if (!usedPlayerIds.has(position.playerId)) {
                usedPlayerIds.add(position.playerId);
                cleanPositions.push({
                    index: index,
                    playerId: position.playerId
                });
            } else {
                console.warn(`⚠️ Joueur dupliqué détecté à la position ${index}: ${position.playerId}`);
                // Garder la position mais sans joueur pour éviter l'erreur
                cleanPositions.push({
                    index: index,
                    playerId: null
                });
            }
        });
        
        return cleanPositions;
    },
    
    cleanFormationData(formation) {
        // Nettoyer et valider les données de formation
        const cleanedFormation = {
            formationType: formation.type || formation.formationType,
            positions: formation.positions || [],
            
            // CORRECTION: Respecter les limites de validation PlayMFL
            depth: this.validateNumber(formation.depth, 1.0, 0.0, 1.3),
            compactness: this.validateNumber(formation.compactness, 0.8, 0.7, 1.3), // Min 0.7 !
            width: this.validateNumber(formation.width, 1.0, 0.0, 2.0),
            
            // Propriétés optionnelles avec valeurs par défaut
            cornerLeftPlayers: formation.cornerLeftPlayers || [],
            cornerRightPlayers: formation.cornerRightPlayers || [],
            penaltyPlayers: formation.penaltyPlayers || [],
            freeKickDirectPlayers: formation.freeKickDirectPlayers || [],
            freeKickIndirectPlayers: formation.freeKickIndirectPlayers || [],
            pitchType: formation.pitchType || "EC24_FRANCE",
            playersInstructions: formation.playersInstructions || {}
        };
        
        // Ajouter les tactiques si elles sont définies
        const tactics = ['pressing', 'directness', 'crosses', 'dribble', 'farShot', 'sideAttackLeft', 
                        'aggressivity', 'clearance', 'tempo', 'offside', 'tackles'];
        
        tactics.forEach(tactic => {
            if (formation[tactic] !== null && formation[tactic] !== undefined) {
                cleanedFormation[tactic] = formation[tactic];
            }
        });
        
        return cleanedFormation;
    },

    validateNumber(value, defaultValue, min, max) {
        if (value === null || value === undefined || isNaN(value)) {
            return defaultValue;
        }
        const numValue = parseFloat(value);
        return Math.max(min, Math.min(max, numValue));
    }
};

console.log('✅ Gestionnaire API chargé (version Dapper)');