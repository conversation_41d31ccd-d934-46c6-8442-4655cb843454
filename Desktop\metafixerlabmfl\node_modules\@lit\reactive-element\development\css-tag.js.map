{"version": 3, "file": "css-tag.js", "sourceRoot": "", "sources": ["../src/css-tag.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;AAE/C;;GAEG;AACH,MAAM,CAAC,MAAM,2BAA2B,GACtC,MAAM,CAAC,UAAU;IACjB,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC;IAC/D,oBAAoB,IAAI,QAAQ,CAAC,SAAS;IAC1C,SAAS,IAAI,aAAa,CAAC,SAAS,CAAC;AAkBvC,MAAM,iBAAiB,GAAG,MAAM,EAAE,CAAC;AAEnC,MAAM,WAAW,GAAG,IAAI,OAAO,EAAuC,CAAC;AAEvE;;;;;;GAMG;AACH,MAAM,OAAO,SAAS;IAOpB,YACE,OAAe,EACf,OAAyC,EACzC,SAAiB;QATnB,4CAA4C;QAC5C,KAAC,cAAc,CAAC,GAAG,IAAI,CAAC;QAUtB,IAAI,SAAS,KAAK,iBAAiB,EAAE;YACnC,MAAM,IAAI,KAAK,CACb,mEAAmE,CACpE,CAAC;SACH;QACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED,0EAA0E;IAC1E,4DAA4D;IAC5D,IAAI,UAAU;QACZ,2EAA2E;QAC3E,iBAAiB;QACjB,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC9B,IAAI,2BAA2B,IAAI,UAAU,KAAK,SAAS,EAAE;YAC3D,MAAM,SAAS,GAAG,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC;YAChE,IAAI,SAAS,EAAE;gBACb,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aACvC;YACD,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC5B,CAAC,IAAI,CAAC,WAAW,GAAG,UAAU,GAAG,IAAI,aAAa,EAAE,CAAC,CAAC,WAAW,CAC/D,IAAI,CAAC,OAAO,CACb,CAAC;gBACF,IAAI,SAAS,EAAE;oBACb,WAAW,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;iBACtC;aACF;SACF;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;CACF;AAUD,MAAM,iBAAiB,GAAG,CAAC,KAA8B,EAAE,EAAE;IAC3D,4CAA4C;IAC5C,IAAK,KAAmB,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;QACjD,OAAQ,KAAmB,CAAC,OAAO,CAAC;KACrC;SAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACpC,OAAO,KAAK,CAAC;KACd;SAAM;QACL,MAAM,IAAI,KAAK,CACb,kEAAkE;YAChE,GAAG,KAAK,8DAA8D;YACtE,0BAA0B,CAC7B,CAAC;KACH;AACH,CAAC,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,KAAc,EAAE,EAAE,CAC1C,IAAK,SAAoC,CACvC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EACjD,SAAS,EACT,iBAAiB,CAClB,CAAC;AAEJ;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,GAAG,GAAG,CACjB,OAA6B,EAC7B,GAAG,MAAmC,EAC3B,EAAE;IACb,MAAM,OAAO,GACX,OAAO,CAAC,MAAM,KAAK,CAAC;QAClB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,MAAM,CAAC,MAAM,CACX,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,EAC9D,OAAO,CAAC,CAAC,CAAC,CACX,CAAC;IACR,OAAO,IAAK,SAAoC,CAC9C,OAAO,EACP,OAAO,EACP,iBAAiB,CAClB,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,CACzB,UAAsB,EACtB,MAAgC,EAChC,EAAE;IACF,IAAI,2BAA2B,EAAE;QAC9B,UAAyB,CAAC,kBAAkB,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAC/D,CAAC,YAAY,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAW,CAC/C,CAAC;KACH;SAAM;QACL,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACnB,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAC9C,8DAA8D;YAC9D,MAAM,KAAK,GAAI,MAAc,CAAC,UAAU,CAAC,CAAC;YAC1C,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACpC;YACD,KAAK,CAAC,WAAW,GAAI,CAAe,CAAC,OAAO,CAAC;YAC7C,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;KACJ;AACH,CAAC,CAAC;AAEF,MAAM,uBAAuB,GAAG,CAAC,KAAoB,EAAE,EAAE;IACvD,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;QACjC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;KACzB;IACD,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC;AAC5B,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAC7B,2BAA2B;IAC3B,CAAC,SAAS,IAAI,MAAM,CAAC,aAAa,KAAK,SAAS,CAAC;IAC/C,CAAC,CAAC,CAAC,CAAoB,EAAE,EAAE,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAoB,EAAE,EAAE,CACvB,CAAC,YAAY,aAAa,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\nconst NODE_MODE = false;\nconst global = NODE_MODE ? globalThis : window;\n\n/**\n * Whether the current browser supports `adoptedStyleSheets`.\n */\nexport const supportsAdoptingStyleSheets: boolean =\n  global.ShadowRoot &&\n  (global.ShadyCSS === undefined || global.ShadyCSS.nativeShadow) &&\n  'adoptedStyleSheets' in Document.prototype &&\n  'replace' in CSSStyleSheet.prototype;\n\n/**\n * A CSSResult or native CSSStyleSheet.\n *\n * In browsers that support constructible CSS style sheets, CSSStyleSheet\n * object can be used for styling along side CSSResult from the `css`\n * template tag.\n */\nexport type CSSResultOrNative = CSSResult | CSSStyleSheet;\n\nexport type CSSResultArray = Array<CSSResultOrNative | CSSResultArray>;\n\n/**\n * A single CSSResult, CSSStyleSheet, or an array or nested arrays of those.\n */\nexport type CSSResultGroup = CSSResultOrNative | CSSResultArray;\n\nconst constructionToken = Symbol();\n\nconst cssTagCache = new WeakMap<TemplateStringsArray, CSSStyleSheet>();\n\n/**\n * A container for a string of CSS text, that may be used to create a CSSStyleSheet.\n *\n * CSSResult is the return value of `css`-tagged template literals and\n * `unsafeCSS()`. In order to ensure that CSSResults are only created via the\n * `css` tag and `unsafeCSS()`, CSSResult cannot be constructed directly.\n */\nexport class CSSResult {\n  // This property needs to remain unminified.\n  ['_$cssResult$'] = true;\n  readonly cssText: string;\n  private _styleSheet?: CSSStyleSheet;\n  private _strings: TemplateStringsArray | undefined;\n\n  private constructor(\n    cssText: string,\n    strings: TemplateStringsArray | undefined,\n    safeToken: symbol\n  ) {\n    if (safeToken !== constructionToken) {\n      throw new Error(\n        'CSSResult is not constructable. Use `unsafeCSS` or `css` instead.'\n      );\n    }\n    this.cssText = cssText;\n    this._strings = strings;\n  }\n\n  // This is a getter so that it's lazy. In practice, this means stylesheets\n  // are not created until the first element instance is made.\n  get styleSheet(): CSSStyleSheet | undefined {\n    // If `supportsAdoptingStyleSheets` is true then we assume CSSStyleSheet is\n    // constructable.\n    let styleSheet = this._styleSheet;\n    const strings = this._strings;\n    if (supportsAdoptingStyleSheets && styleSheet === undefined) {\n      const cacheable = strings !== undefined && strings.length === 1;\n      if (cacheable) {\n        styleSheet = cssTagCache.get(strings);\n      }\n      if (styleSheet === undefined) {\n        (this._styleSheet = styleSheet = new CSSStyleSheet()).replaceSync(\n          this.cssText\n        );\n        if (cacheable) {\n          cssTagCache.set(strings, styleSheet);\n        }\n      }\n    }\n    return styleSheet;\n  }\n\n  toString(): string {\n    return this.cssText;\n  }\n}\n\ntype ConstructableCSSResult = CSSResult & {\n  new (\n    cssText: string,\n    strings: TemplateStringsArray | undefined,\n    safeToken: symbol\n  ): CSSResult;\n};\n\nconst textFromCSSResult = (value: CSSResultGroup | number) => {\n  // This property needs to remain unminified.\n  if ((value as CSSResult)['_$cssResult$'] === true) {\n    return (value as CSSResult).cssText;\n  } else if (typeof value === 'number') {\n    return value;\n  } else {\n    throw new Error(\n      `Value passed to 'css' function must be a 'css' function result: ` +\n        `${value}. Use 'unsafeCSS' to pass non-literal values, but take care ` +\n        `to ensure page security.`\n    );\n  }\n};\n\n/**\n * Wrap a value for interpolation in a {@linkcode css} tagged template literal.\n *\n * This is unsafe because untrusted CSS text can be used to phone home\n * or exfiltrate data to an attacker controlled site. Take care to only use\n * this with trusted input.\n */\nexport const unsafeCSS = (value: unknown) =>\n  new (CSSResult as ConstructableCSSResult)(\n    typeof value === 'string' ? value : String(value),\n    undefined,\n    constructionToken\n  );\n\n/**\n * A template literal tag which can be used with LitElement's\n * {@linkcode LitElement.styles} property to set element styles.\n *\n * For security reasons, only literal string values and number may be used in\n * embedded expressions. To incorporate non-literal values {@linkcode unsafeCSS}\n * may be used inside an expression.\n */\nexport const css = (\n  strings: TemplateStringsArray,\n  ...values: (CSSResultGroup | number)[]\n): CSSResult => {\n  const cssText =\n    strings.length === 1\n      ? strings[0]\n      : values.reduce(\n          (acc, v, idx) => acc + textFromCSSResult(v) + strings[idx + 1],\n          strings[0]\n        );\n  return new (CSSResult as ConstructableCSSResult)(\n    cssText,\n    strings,\n    constructionToken\n  );\n};\n\n/**\n * Applies the given styles to a `shadowRoot`. When Shadow DOM is\n * available but `adoptedStyleSheets` is not, styles are appended to the\n * `shadowRoot` to [mimic spec behavior](https://wicg.github.io/construct-stylesheets/#using-constructed-stylesheets).\n * Note, when shimming is used, any styles that are subsequently placed into\n * the shadowRoot should be placed *before* any shimmed adopted styles. This\n * will match spec behavior that gives adopted sheets precedence over styles in\n * shadowRoot.\n */\nexport const adoptStyles = (\n  renderRoot: ShadowRoot,\n  styles: Array<CSSResultOrNative>\n) => {\n  if (supportsAdoptingStyleSheets) {\n    (renderRoot as ShadowRoot).adoptedStyleSheets = styles.map((s) =>\n      s instanceof CSSStyleSheet ? s : s.styleSheet!\n    );\n  } else {\n    styles.forEach((s) => {\n      const style = document.createElement('style');\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const nonce = (global as any)['litNonce'];\n      if (nonce !== undefined) {\n        style.setAttribute('nonce', nonce);\n      }\n      style.textContent = (s as CSSResult).cssText;\n      renderRoot.appendChild(style);\n    });\n  }\n};\n\nconst cssResultFromStyleSheet = (sheet: CSSStyleSheet) => {\n  let cssText = '';\n  for (const rule of sheet.cssRules) {\n    cssText += rule.cssText;\n  }\n  return unsafeCSS(cssText);\n};\n\nexport const getCompatibleStyle =\n  supportsAdoptingStyleSheets ||\n  (NODE_MODE && global.CSSStyleSheet === undefined)\n    ? (s: CSSResultOrNative) => s\n    : (s: CSSResultOrNative) =>\n        s instanceof CSSStyleSheet ? cssResultFromStyleSheet(s) : s;\n"]}