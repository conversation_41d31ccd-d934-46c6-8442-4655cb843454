// ===============================================
// FONCTIONS GLOBALES - PlayMFL Manager
// Fonctions appelées directement depuis le HTML
// ===============================================

// ===============================================
// GESTION DES ACCORDÉONS D'ÉQUIPES
// ===============================================

function toggleTeamCard(teamIndex) {
    const teamCard = document.querySelector(`.team-card:nth-child(${teamIndex + 1})`);
    if (teamCard) {
        teamCard.classList.toggle('expanded');
        
        // Animation de l'icône
        const expandIcon = teamCard.querySelector('.expand-icon');
        if (expandIcon) {
            expandIcon.style.transform = teamCard.classList.contains('expanded') 
                ? 'rotate(180deg)' 
                : 'rotate(0deg)';
        }
        
        console.log(`📂 Équipe ${teamIndex} ${teamCard.classList.contains('expanded') ? 'étendue' : 'réduite'}`);
    }
}

function toggleTacticsSection(teamIndex) {
    const teamCard = document.querySelector(`.team-card:nth-child(${teamIndex + 1})`);
    if (teamCard) {
        const tacticsSection = teamCard.querySelector('.tactics-section');
        if (tacticsSection) {
            tacticsSection.classList.toggle('expanded');
            
            // Animation de l'icône
            const expandIcon = tacticsSection.querySelector('.tactics-expand-icon');
            if (expandIcon) {
                expandIcon.style.transform = tacticsSection.classList.contains('expanded') 
                    ? 'rotate(180deg)' 
                    : 'rotate(0deg)';
            }
            
            console.log(`⚙️ Tactiques équipe ${teamIndex} ${tacticsSection.classList.contains('expanded') ? 'étendues' : 'réduites'}`);
        }
    }
}

// ===============================================
// ACTIONS DE ROTATION
// ===============================================



async function smartRotateTeam(teamIndex) {
    try {
        UIManager.showMessage('info', '🔄 Rotation intelligente complète (remplacements + échanges)...');
        const result = await RotationSystem.rotateTeamAll(teamIndex);

        if (result && result.rotationCount > 0) {
            let message = `✅ ${result.rotationCount} changement(s) effectué(s)`;
            if (result.swapCount > 0) {
                message += ` (${result.swapCount} échange(s) optimaux, +${Math.round(result.totalImprovement)} pts)`;
            }
            UIManager.showMessage('success', message);
        } else {
            UIManager.showMessage('info', `ℹ️ Aucune amélioration nécessaire`);
        }

    } catch (error) {
        console.error('❌ Erreur rotation intelligente:', error);
        UIManager.showMessage('error', `❌ Erreur: ${error.message}`);
    }
}



// ===============================================
// GESTION DES FORMATIONS NOMMÉES
// ===============================================

async function applyPresetToTeam(teamIndex, presetName) {
    try {
        const team = TeamManager.getTeam(teamIndex);
        const formations = TeamManager.getNamedFormations(team.clubId, team.squadId);
        const preset = formations.find(f => f.name === presetName);
        
        if (!preset) {
            UIManager.showMessage('error', `❌ Préréglage "${presetName}" introuvable`);
            return;
        }
        
        UIManager.showMessage('info', `🎮 Application du préréglage "${presetName}"...`);
        
        const success = await ApiManager.applyFormationToTeam(team, preset);
        
        if (success) {
            // Recharger la formation mise à jour
            const updatedFormation = await ApiManager.loadFormationForSquad(team.clubId, team.squadId);
            if (updatedFormation) {
                team.formation = updatedFormation;
                UIManager.updateTeamCard(teamIndex);
            }
            
            UIManager.showMessage('success', `✅ Préréglage "${presetName}" appliqué avec succès !`);
        } else {
            UIManager.showMessage('error', `❌ Échec de l'application du préréglage "${presetName}"`);
        }
        
    } catch (error) {
        console.error('❌ Erreur application préréglage:', error);
        UIManager.showMessage('error', `❌ Erreur: ${error.message}`);
    }
}

// ===============================================
// SAUVEGARDE DES FORMATIONS
// ===============================================

async function saveFormation(teamIndex) {
    try {
        const team = TeamManager.getTeam(teamIndex);
        
        // Trouver le bouton de sauvegarde et montrer le loading
        const teamCard = document.querySelector(`.team-card:nth-child(${teamIndex + 1})`);
        const saveButton = teamCard?.querySelector('.save-header');
        
        if (saveButton) {
            const originalText = saveButton.innerHTML;
            saveButton.innerHTML = '<div class="loading"></div> Sauvegarde...';
            saveButton.disabled = true;
        }
        
        UIManager.showMessage('info', `💾 Sauvegarde de ${team.squadName}...`);
        
        const result = await ApiManager.saveFormation(team);
        
        if (result) {
            UIManager.showMessage('success', `✅ ${team.squadName} sauvegardée avec succès !`);
            
            // Marquer comme sauvegardé
            if (saveButton) {
                saveButton.classList.remove('has-changes');
            }
        } else {
            UIManager.showMessage('error', `❌ Échec de la sauvegarde de ${team.squadName}`);
        }
        
    } catch (error) {
        console.error('❌ Erreur sauvegarde:', error);
        UIManager.showMessage('error', `❌ Erreur: ${error.message}`);
    } finally {
        // Restaurer le bouton
        const teamCard = document.querySelector(`.team-card:nth-child(${teamIndex + 1})`);
        const saveButton = teamCard?.querySelector('.save-header');
        
        if (saveButton) {
            saveButton.innerHTML = '💾 Sauvegarder';
            saveButton.disabled = false;
        }
    }
}

// ===============================================
// CONTRÔLES TACTIQUES
// ===============================================

function updateSlider(teamIndex, tacticKey, value) {
    const team = TeamManager.getTeam(teamIndex);
    const numValue = parseFloat(value);
    
    if (team.formation) {
        team.formation[tacticKey] = numValue;
        
        // Mettre à jour l'affichage de la valeur
        const teamCard = document.querySelector(`.team-card:nth-child(${teamIndex + 1})`);
        const valueDisplay = teamCard?.querySelector(`[onchange*="${tacticKey}"]`)?.parentElement?.querySelector('.slider-value');
        if (valueDisplay) {
            valueDisplay.textContent = numValue.toFixed(1);
        }
        
        // Marquer comme modifié
        markFormationAsChanged(teamIndex);
        
        console.log(`🎯 ${team.squadName}: ${tacticKey} = ${numValue}`);
    }
}

function adjustSlider(teamIndex, tacticKey, adjustment) {
    const team = TeamManager.getTeam(teamIndex);
    const rule = TACTIC_RULES[tacticKey];
    
    if (team.formation && rule) {
        const currentValue = team.formation[tacticKey] || 0;
        const newValue = Math.max(rule.min, Math.min(rule.max, currentValue + adjustment));
        
        team.formation[tacticKey] = newValue;
        
        // Mettre à jour le slider et l'affichage
        const teamCard = document.querySelector(`.team-card:nth-child(${teamIndex + 1})`);
        const slider = teamCard?.querySelector(`input[onchange*="${tacticKey}"]`);
        const valueDisplay = teamCard?.querySelector(`[onchange*="${tacticKey}"]`)?.parentElement?.querySelector('.slider-value');
        
        if (slider) slider.value = newValue;
        if (valueDisplay) valueDisplay.textContent = newValue.toFixed(1);
        
        // Marquer comme modifié
        markFormationAsChanged(teamIndex);
        
        console.log(`🎯 ${team.squadName}: ${tacticKey} ajusté à ${newValue}`);
    }
}

function setButtons3(teamIndex, tacticKey, buttonIndex) {
    const team = TeamManager.getTeam(teamIndex);
    const rule = TACTIC_RULES[tacticKey];
    
    if (team.formation && rule && rule.values[buttonIndex] !== undefined) {
        team.formation[tacticKey] = rule.values[buttonIndex];
        
        // Mettre à jour l'affichage
        const teamCard = document.querySelector(`.team-card:nth-child(${teamIndex + 1})`);
        const buttonsContainer = teamCard?.querySelector(`[onclick*="setButtons3(${teamIndex}, '${tacticKey}'"]`)?.parentElement;
        const displayElement = buttonsContainer?.parentElement?.querySelector('.buttons3-display');
        
        if (displayElement) {
            displayElement.textContent = rule.display[buttonIndex];
        }
        
        // Mettre à jour les états des boutons
        if (buttonsContainer) {
            const buttons = buttonsContainer.querySelectorAll('.btn3');
            buttons.forEach((btn, i) => {
                btn.classList.toggle('active', i === buttonIndex);
            });
        }
        
        // Marquer comme modifié
        markFormationAsChanged(teamIndex);
        
        console.log(`🎯 ${team.squadName}: ${tacticKey} = ${rule.labels[buttonIndex]} (${rule.values[buttonIndex]})`);
    }
}

// ===============================================
// UTILITAIRES
// ===============================================

function markFormationAsChanged(teamIndex) {
    const teamCard = document.querySelector(`.team-card:nth-child(${teamIndex + 1})`);
    const saveButton = teamCard?.querySelector('.save-header');
    
    if (saveButton) {
        saveButton.classList.add('has-changes');
    }
}

// ===============================================
// EXPORT/IMPORT ET AIDE
// ===============================================

function exportSettings() {
    try {
        const data = StorageManager.exportData();
        if (data) {
            // Créer un blob et télécharger
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `playmfl-settings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            UIManager.showMessage('success', '✅ Paramètres exportés avec succès !');
        } else {
            UIManager.showMessage('error', '❌ Erreur lors de l\'export des paramètres');
        }
    } catch (error) {
        console.error('❌ Erreur export:', error);
        UIManager.showMessage('error', `❌ Erreur: ${error.message}`);
    }
}

function showHelp() {
    const helpModal = document.getElementById('helpModal');
    if (helpModal) {
        helpModal.style.display = 'flex';
    }
}

function showAbout() {
    UIManager.showMessage('info', `
        <strong>PlayMFL Manager v2.0</strong><br>
        Gestionnaire intelligent d'équipes PlayMFL<br><br>
        <strong>Fonctionnalités:</strong><br>
        • Rotations automatiques (fatigue, cartons)<br>
        • Optimisation intelligente des formations<br>
        • Contrôles tactiques avancés<br>
        • Application de préréglages<br>
        • Sauvegarde en temps réel<br><br>
        <em>Développé pour optimiser votre expérience PlayMFL</em>
    `);
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// ===============================================
// FONCTIONS DE DÉBOGAGE
// ===============================================

function debugTeam(teamIndex) {
    const team = TeamManager.getTeam(teamIndex);
    console.log('🔍 Debug équipe:', team);
    
    const clubPlayers = TeamManager.getClubPlayers(team.clubId);
    console.log('👥 Joueurs du club:', clubPlayers.length);
    
    const formations = TeamManager.getNamedFormations(team.clubId, team.squadId);
    console.log('🎮 Préréglages:', formations.length);
    
    if (team.formation) {
        const stats = PlayerSystem.calculateTeamStats(team, clubPlayers);
        console.log('📊 Statistiques équipe:', stats);
    }
}

// Ajouter une fonction de debug globale
window.debugPlayMFL = {
    team: debugTeam,
    allTeams: () => console.log('🏆 Toutes les équipes:', TeamManager.getAllTeams()),
    settings: () => console.log('⚙️ Paramètres:', StorageManager.loadSettings()),
    token: () => console.log('🔑 Token présent:', !!ApiManager.getToken())
};

// ===============================================
// FONCTIONS DE DEBUG POUR LES POSITIONS
// ===============================================

window.debugPositions = function(teamIndex, targetPosition) {
    const team = App.getTeam(teamIndex);
    if (!team) {
        console.error('❌ Équipe non trouvée');
        return;
    }

    const clubPlayers = TeamManager.getClubPlayers(team.clubId);
    console.log(`🔍 Debug positions pour ${team.squadName} - Position: ${targetPosition}`);
    console.log(`📊 ${clubPlayers.length} joueurs disponibles dans le club`);

    // Analyser tous les joueurs pour cette position
    const analysis = clubPlayers.map(player => {
        const debug = PlayerSystem.debugPlayerForPosition(player, targetPosition);
        return {
            player,
            ...debug
        };
    }).filter(p => p.canPlay)
      .sort((a, b) => {
          if (b.compatibility !== a.compatibility) return b.compatibility - a.compatibility;
          return b.rating - a.rating;
      });

    console.log(`\n🎯 Top 5 candidats pour ${targetPosition}:`);
    analysis.slice(0, 5).forEach((candidate, index) => {
        const p = candidate.player;
        console.log(`${index + 1}. ${p.metadata?.firstName} ${p.metadata?.lastName}`);
        console.log(`   📋 Positions: [${p.metadata?.positions?.join(', ')}]`);
        console.log(`   🎯 Compatibilité: ${candidate.compatibility}/100`);
        console.log(`   ⭐ Rating: ${candidate.rating}`);
        console.log(`   ⚡ Énergie: ${p.energy}/10000`);
        console.log('');
    });

    return analysis;
};

window.testRotationLogic = function(teamIndex) {
    const team = App.getTeam(teamIndex);
    if (!team) {
        console.error('❌ Équipe non trouvée');
        return;
    }

    console.log(`🧪 Test de la logique de rotation pour ${team.squadName}`);

    if (team.formation && team.formation.positions) {
        const formationType = team.formation.type;
        const expectedPositions = FORMATION_TO_POSITIONS[formationType] || [];

        console.log(`📐 Formation: ${formationType}`);
        console.log(`📋 Positions attendues: [${expectedPositions.join(', ')}]`);

        team.formation.positions.forEach((position, index) => {
            const targetPosition = expectedPositions[position.index || index] || 'CM';
            console.log(`\n🔍 Position ${index + 1}: ${targetPosition}`);
            debugPositions(teamIndex, targetPosition);
        });
    }
};

// ===============================================
// OUTILS DE SIMULATION POUR TESTS
// ===============================================

window.simulateFatigue = function(teamIndex, playerIds = [], energyLevel = 3000) {
    const team = App.getTeam(teamIndex);
    if (!team) {
        console.error('❌ Équipe non trouvée');
        return;
    }

    const clubPlayers = TeamManager.getClubPlayers(team.clubId);
    let modifiedCount = 0;

    if (playerIds.length === 0) {
        // Si aucun joueur spécifié, prendre les 3 premiers de l'équipe
        if (team.formation && team.formation.positions) {
            playerIds = team.formation.positions.slice(0, 3).map(p => p.playerId);
        }
    }

    playerIds.forEach(playerId => {
        const player = clubPlayers.find(p => p.id === playerId);
        if (player) {
            const originalEnergy = player.energy;
            player.energy = energyLevel;
            console.log(`⚡ ${player.metadata?.firstName} ${player.metadata?.lastName}: ${originalEnergy} → ${energyLevel} énergie`);
            modifiedCount++;
        }
    });

    console.log(`🎭 Simulation: ${modifiedCount} joueur(s) fatigué(s) à ${energyLevel}/10000`);
    UIManager.updateTeamCard(teamIndex);

    return { modifiedCount, energyLevel };
};

window.simulateCards = function(teamIndex, playerIds = []) {
    const team = App.getTeam(teamIndex);
    if (!team) {
        console.error('❌ Équipe non trouvée');
        return;
    }

    const clubPlayers = TeamManager.getClubPlayers(team.clubId);
    let modifiedCount = 0;

    if (playerIds.length === 0) {
        // Si aucun joueur spécifié, prendre 2 joueurs de l'équipe
        if (team.formation && team.formation.positions) {
            playerIds = team.formation.positions.slice(0, 2).map(p => p.playerId);
        }
    }

    playerIds.forEach(playerId => {
        const player = clubPlayers.find(p => p.id === playerId);
        if (player) {
            // Simuler une suspension
            if (!player.matchesSuspensions) {
                player.matchesSuspensions = [];
            }
            player.matchesSuspensions.push({
                match: { status: "PLANNED" },
                reason: "SIMULATION_TEST"
            });
            console.log(`🟥 ${player.metadata?.firstName} ${player.metadata?.lastName}: Carton simulé`);
            modifiedCount++;
        }
    });

    console.log(`🎭 Simulation: ${modifiedCount} joueur(s) cartonné(s)`);
    UIManager.updateTeamCard(teamIndex);

    return { modifiedCount };
};

window.resetSimulation = function(teamIndex) {
    const team = App.getTeam(teamIndex);
    if (!team) {
        console.error('❌ Équipe non trouvée');
        return;
    }

    const clubPlayers = TeamManager.getClubPlayers(team.clubId);
    let resetCount = 0;

    clubPlayers.forEach(player => {
        // Remettre l'énergie à fond
        if (player.energy < 10000) {
            player.energy = 10000;
            resetCount++;
        }

        // Supprimer les cartons simulés
        if (player.matchesSuspensions) {
            const originalLength = player.matchesSuspensions.length;
            player.matchesSuspensions = player.matchesSuspensions.filter(
                suspension => suspension.reason !== "SIMULATION_TEST"
            );
            if (player.matchesSuspensions.length !== originalLength) {
                resetCount++;
            }
        }
    });

    console.log(`🔄 Reset: ${resetCount} modification(s) annulée(s)`);
    UIManager.updateTeamCard(teamIndex);

    return { resetCount };
};

window.testRotationComplete = function(teamIndex) {
    console.log('🧪 TEST COMPLET DE ROTATION AVEC RÉORGANISATION');
    console.log('===============================================');

    const team = App.getTeam(teamIndex);
    if (!team) {
        console.error('❌ Équipe non trouvée');
        return;
    }

    console.log(`📋 Équipe: ${team.squadName}`);

    // 1. État initial
    console.log('\n1️⃣ ÉTAT INITIAL:');
    analyzeCurrentFormation(teamIndex);

    // 2. Simuler fatigue
    console.log('\n2️⃣ SIMULATION FATIGUE:');
    simulateFatigue(teamIndex, [], 2000); // 20% énergie

    // 3. Simuler cartons
    console.log('\n3️⃣ SIMULATION CARTONS:');
    simulateCards(teamIndex);

    // 4. Analyser les problèmes
    console.log('\n4️⃣ ANALYSE DES PROBLÈMES:');
    analyzeCurrentFormation(teamIndex);

    // 5. Test rotation intelligente avec réorganisation
    console.log('\n5️⃣ ROTATION INTELLIGENTE AVEC RÉORGANISATION:');
    console.log('Appel de smartRotateTeam (nouvelle version)...');
    smartRotateTeam(teamIndex);

    // 6. Analyser le résultat
    setTimeout(() => {
        console.log('\n6️⃣ ANALYSE POST-ROTATION:');
        analyzeCurrentFormation(teamIndex);

        // 7. Reset
        setTimeout(() => {
            console.log('\n7️⃣ RESET SIMULATION:');
            resetSimulation(teamIndex);
            console.log('✅ Test terminé !');
        }, 1000);
    }, 2000);
};

window.analyzeCurrentFormation = function(teamIndex) {
    const team = App.getTeam(teamIndex);
    if (!team || !team.formation) {
        console.error('❌ Équipe ou formation non trouvée');
        return;
    }

    const clubPlayers = TeamManager.getClubPlayers(team.clubId);
    const formationType = team.formation.type;
    const expectedPositions = FORMATION_TO_POSITIONS[formationType] || [];

    console.log(`📐 Formation: ${formationType}`);
    console.log(`📊 Analyse des positions:`);

    // Trier les positions par leur index pour avoir l'ordre correct
    const sortedPositions = [...team.formation.positions].sort((a, b) => {
        const indexA = a.index !== undefined ? a.index : 0;
        const indexB = b.index !== undefined ? b.index : 0;
        return indexA - indexB;
    });

    console.log(`🔍 DEBUG: Positions dans l'ordre de la formation:`);
    team.formation.positions.forEach((position, arrayIndex) => {
        const positionIndex = position.index !== undefined ? position.index : arrayIndex;
        const targetPosition = expectedPositions[positionIndex] || 'CM';
        const player = clubPlayers.find(p => p.id === position.playerId);
        const playerName = player ? `${player.metadata?.firstName?.charAt(0) || '?'}. ${player.metadata?.lastName || 'Inconnu'}` : 'Inconnu';
        console.log(`   Array[${arrayIndex}] → Index[${positionIndex}] → ${targetPosition}: ${playerName} (ID: ${position.playerId})`);
    });

    console.log(`\n📊 Analyse par position de jeu:`);
    sortedPositions.forEach((position, index) => {
        const player = clubPlayers.find(p => p.id === position.playerId);
        const positionIndex = position.index !== undefined ? position.index : index;
        const targetPosition = expectedPositions[positionIndex] || 'CM';

        if (player) {
            const rating = PlayerSystem.calculatePositionRating(player, targetPosition);
            const familiarity = PlayerSystem.getPositionFamiliarity(player, targetPosition);
            const energyPercent = Math.round((player.energy / 10000) * 100);
            const isSuspended = PlayerSystem.isPlayerSuspended(player);

            let status = '✅';
            if (isSuspended) status = '🟥';
            else if (energyPercent < 30) status = '⚡';
            else if (rating < 50) status = '⚠️';

            const playerName = `${player.metadata?.firstName?.charAt(0) || '?'}. ${player.metadata?.lastName || 'Inconnu'}`;
            console.log(`   ${status} ${targetPosition}: ${playerName} (Index: ${positionIndex})`);
            console.log(`      📊 Rating: ${rating} | 🎯 Familiarité: ${familiarity} | ⚡ Énergie: ${energyPercent}%`);
            console.log(`      🔍 Positions naturelles: [${player.metadata?.positions?.join(', ') || 'Aucune'}]`);
        }
    });
};

window.debugPlayerPositions = function(teamIndex) {
    const team = App.getTeam(teamIndex);
    if (!team) {
        console.error('❌ Équipe non trouvée');
        return;
    }

    const clubPlayers = TeamManager.getClubPlayers(team.clubId);
    console.log(`🔍 DEBUG POSITIONS JOUEURS - ${team.squadName}`);
    console.log('='.repeat(50));

    clubPlayers.slice(0, 5).forEach(player => {
        const playerName = `${player.metadata?.firstName?.charAt(0) || '?'}. ${player.metadata?.lastName || 'Inconnu'}`;
        console.log(`👤 ${playerName}`);
        console.log(`   📋 Positions API: [${player.metadata?.positions?.join(', ') || 'Aucune'}]`);
        console.log(`   📊 Overall: ${player.metadata?.overall || 'N/A'}`);

        // Tester quelques positions
        const testPositions = ['GK', 'CB', 'LB', 'CM', 'CAM', 'ST'];
        testPositions.forEach(pos => {
            const familiarity = PlayerSystem.getPositionFamiliarity(player, pos);
            const rating = PlayerSystem.calculatePositionRating(player, pos);
            console.log(`      ${pos}: ${familiarity} (Rating: ${rating})`);
        });
        console.log('');
    });
};

window.findOptimalSwaps = function(teamIndex) {
    const team = App.getTeam(teamIndex);
    if (!team || !team.formation) {
        console.error('❌ Équipe ou formation non trouvée');
        return;
    }

    const clubPlayers = TeamManager.getClubPlayers(team.clubId);
    const formationType = team.formation.type;
    const expectedPositions = FORMATION_TO_POSITIONS[formationType] || [];

    console.log(`🔍 RECHERCHE D'ÉCHANGES OPTIMAUX - ${team.squadName}`);
    console.log('='.repeat(60));

    // Analyser chaque position pour trouver des améliorations possibles
    const improvements = [];

    for (let i = 0; i < team.formation.positions.length; i++) {
        const position = team.formation.positions[i];
        const player = clubPlayers.find(p => p.id === position.playerId);
        const positionIndex = position.index !== undefined ? position.index : i;
        const targetPosition = expectedPositions[positionIndex] || 'CM';

        if (!player) continue;

        const currentRating = PlayerSystem.calculatePositionRating(player, targetPosition);
        const familiarity = PlayerSystem.getPositionFamiliarity(player, targetPosition);

        const playerName = `${player.metadata?.firstName?.charAt(0) || '?'}. ${player.metadata?.lastName || 'Inconnu'}`;
        console.log(`\n📍 Position ${i}: ${targetPosition} - ${playerName}`);
        console.log(`   📊 Rating actuel: ${currentRating} | Familiarité: ${familiarity}`);

        // Chercher des échanges possibles avec d'autres positions
        for (let j = 0; j < team.formation.positions.length; j++) {
            if (i === j) continue;

            const otherPosition = team.formation.positions[j];
            const otherPlayer = clubPlayers.find(p => p.id === otherPosition.playerId);
            const otherPositionIndex = otherPosition.index !== undefined ? otherPosition.index : j;
            const otherTargetPosition = expectedPositions[otherPositionIndex] || 'CM';

            if (!otherPlayer) continue;

            // Calculer les ratings si on échange
            const playerInOtherPos = PlayerSystem.calculatePositionRating(player, otherTargetPosition);
            const otherPlayerInCurrentPos = PlayerSystem.calculatePositionRating(otherPlayer, targetPosition);
            const otherPlayerCurrentRating = PlayerSystem.calculatePositionRating(otherPlayer, otherTargetPosition);

            const currentTotal = currentRating + otherPlayerCurrentRating;
            const swapTotal = playerInOtherPos + otherPlayerInCurrentPos;
            const improvement = swapTotal - currentTotal;

            if (improvement > 5) {
                improvements.push({
                    pos1: i,
                    pos2: j,
                    player1: player,
                    player2: otherPlayer,
                    targetPos1: targetPosition,
                    targetPos2: otherTargetPosition,
                    currentTotal,
                    swapTotal,
                    improvement,
                    description: `Échanger ${player.metadata?.firstName?.charAt(0) || '?'}. ${player.metadata?.lastName || 'Inconnu'} (${targetPosition}) avec ${otherPlayer.metadata?.firstName?.charAt(0) || '?'}. ${otherPlayer.metadata?.lastName || 'Inconnu'} (${otherTargetPosition}) = +${Math.round(improvement)} points`
                });
            }
        }
    }

    // Trier par amélioration décroissante
    improvements.sort((a, b) => b.improvement - a.improvement);

    console.log(`\n🎯 ÉCHANGES RECOMMANDÉS (${improvements.length} trouvé(s)):`);
    improvements.slice(0, 5).forEach((swap, index) => {
        console.log(`${index + 1}. ${swap.description}`);
        console.log(`   📊 Total actuel: ${Math.round(swap.currentTotal)} → Après échange: ${Math.round(swap.swapTotal)}`);
    });

    return improvements;
};

window.applyOptimalSwap = async function(teamIndex) {
    const team = App.getTeam(teamIndex);
    if (!team || !team.formation) {
        console.error('❌ Équipe ou formation non trouvée');
        return;
    }

    console.log(`🔄 APPLICATION DE L'ÉCHANGE OPTIMAL - ${team.squadName}`);
    console.log('='.repeat(50));

    // Trouver le meilleur échange
    const improvements = findOptimalSwaps(teamIndex);
    if (improvements.length === 0) {
        console.log('ℹ️ Aucun échange bénéfique trouvé');
        return;
    }

    const bestSwap = improvements[0];
    console.log(`🎯 Application de: ${bestSwap.description}`);
    console.log(`📊 Gain attendu: +${Math.round(bestSwap.improvement)} points`);

    // Créer la nouvelle formation avec l'échange
    const newPositions = [...team.formation.positions];

    // Échanger les joueurs
    const tempPlayerId = newPositions[bestSwap.pos1].playerId;
    newPositions[bestSwap.pos1] = {
        index: newPositions[bestSwap.pos1].index,
        playerId: newPositions[bestSwap.pos2].playerId
    };
    newPositions[bestSwap.pos2] = {
        index: newPositions[bestSwap.pos2].index,
        playerId: tempPlayerId
    };

    // Appliquer l'échange
    try {
        const updatedFormation = await ApiManager.updateFormationPositions(team, newPositions);
        if (updatedFormation) {
            team.formation = updatedFormation;
            UIManager.updateTeamCard(teamIndex);

            console.log('✅ Échange appliqué avec succès !');
            const player1Name = `${bestSwap.player1.metadata?.firstName?.charAt(0) || '?'}. ${bestSwap.player1.metadata?.lastName || 'Inconnu'}`;
            const player2Name = `${bestSwap.player2.metadata?.firstName?.charAt(0) || '?'}. ${bestSwap.player2.metadata?.lastName || 'Inconnu'}`;
            console.log(`🔄 ${player1Name} → ${bestSwap.targetPos2}`);
            console.log(`🔄 ${player2Name} → ${bestSwap.targetPos1}`);

            // Analyser le résultat
            setTimeout(() => {
                console.log('\n📊 ANALYSE POST-ÉCHANGE:');
                analyzeCurrentFormation(teamIndex);
            }, 1000);
        }
    } catch (error) {
        console.error('❌ Erreur lors de l\'échange:', error);
    }
};

window.optimizeAllPositions = async function(teamIndex) {
    const team = App.getTeam(teamIndex);
    if (!team || !team.formation) {
        console.error('❌ Équipe ou formation non trouvée');
        return;
    }

    console.log(`🧠 OPTIMISATION COMPLÈTE DES POSITIONS - ${team.squadName}`);
    console.log('='.repeat(60));

    let totalImprovements = 0;
    let swapCount = 0;

    // Appliquer les échanges un par un jusqu'à ce qu'il n'y en ait plus
    for (let i = 0; i < 5; i++) { // Maximum 5 échanges pour éviter les boucles infinies
        const improvements = findOptimalSwaps(teamIndex);

        if (improvements.length === 0 || improvements[0].improvement < 5) {
            console.log(`✅ Optimisation terminée après ${swapCount} échange(s)`);
            break;
        }

        const bestSwap = improvements[0];
        console.log(`\n🔄 Échange ${i + 1}: ${bestSwap.description}`);

        // Appliquer l'échange
        const newPositions = [...team.formation.positions];
        const tempPlayerId = newPositions[bestSwap.pos1].playerId;
        newPositions[bestSwap.pos1] = {
            index: newPositions[bestSwap.pos1].index,
            playerId: newPositions[bestSwap.pos2].playerId
        };
        newPositions[bestSwap.pos2] = {
            index: newPositions[bestSwap.pos2].index,
            playerId: tempPlayerId
        };

        try {
            const updatedFormation = await ApiManager.updateFormationPositions(team, newPositions);
            if (updatedFormation) {
                team.formation = updatedFormation;
                totalImprovements += bestSwap.improvement;
                swapCount++;

                // Attendre un peu avant le prochain échange
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        } catch (error) {
            console.error('❌ Erreur lors de l\'échange:', error);
            break;
        }
    }

    UIManager.updateTeamCard(teamIndex);
    console.log(`\n🎉 OPTIMISATION TERMINÉE !`);
    console.log(`📊 ${swapCount} échange(s) effectué(s)`);
    console.log(`📈 Gain total estimé: +${Math.round(totalImprovements)} points`);

    // Analyse finale
    setTimeout(() => {
        console.log('\n📊 ANALYSE FINALE:');
        analyzeCurrentFormation(teamIndex);
    }, 1000);
};

window.debugFormationMapping = function(teamIndex) {
    const team = App.getTeam(teamIndex);
    if (!team || !team.formation) {
        console.error('❌ Équipe ou formation non trouvée');
        return;
    }

    const clubPlayers = TeamManager.getClubPlayers(team.clubId);
    const formationType = team.formation.type;
    const expectedPositions = FORMATION_TO_POSITIONS[formationType] || [];

    console.log(`🔍 DEBUG MAPPING FORMATION - ${team.squadName}`);
    console.log(`📐 Formation: ${formationType}`);
    console.log(`📋 Formation ID: ${team.formation.id}`);
    console.log('='.repeat(60));

    // Mapping des IDs que tu as fournis pour vérification
    const currentMapping = {
        0: 395,    // G
        1: 16651,  // DLG
        2: 55902,  // DC
        3: 66155,  // DC
        4: 57617,  // DC
        5: 16636,  // DLD
        6: 84948,  // MG
        7: 55589,  // MDC
        8: 153874, // MOC
        9: 33178,  // MD
        10: 57270  // BU
    };

    console.log('📊 MAPPING ATTENDU vs RÉEL:');
    expectedPositions.forEach((expectedPos, posIndex) => {
        const expectedPlayerId = currentMapping[posIndex];
        const actualPosition = team.formation.positions.find(p => p.index === posIndex);
        const actualPlayer = clubPlayers.find(p => p.id === (actualPosition?.playerId || 0));
        const expectedPlayer = clubPlayers.find(p => p.id === expectedPlayerId);

        console.log(`\n${posIndex}: ${expectedPos}`);
        console.log(`   🎯 Attendu: ${expectedPlayer ? `${expectedPlayer.metadata?.firstName?.charAt(0)}.${expectedPlayer.metadata?.lastName}` : 'Inconnu'} (ID: ${expectedPlayerId})`);
        console.log(`   🎮 Réel: ${actualPlayer ? `${actualPlayer.metadata?.firstName?.charAt(0)}.${actualPlayer.metadata?.lastName}` : 'Inconnu'} (ID: ${actualPosition?.playerId || 'N/A'})`);
        console.log(`   ✅ Match: ${actualPosition?.playerId === expectedPlayerId ? 'OUI' : 'NON'}`);
    });

    console.log('\n📋 POSITIONS DANS LE TABLEAU:');
    team.formation.positions.forEach((position, arrayIndex) => {
        const player = clubPlayers.find(p => p.id === position.playerId);
        const playerName = player ? `${player.metadata?.firstName?.charAt(0)}.${player.metadata?.lastName}` : 'Inconnu';
        const expectedPos = expectedPositions[position.index] || 'CM';
        console.log(`   Array[${arrayIndex}] → Index[${position.index}] → ${expectedPos}: ${playerName} (ID: ${position.playerId})`);
    });
};

window.discoverFormationMappings = function() {
    console.log('🔍 DÉCOUVERTE AUTOMATIQUE DES MAPPINGS DE FORMATIONS');
    console.log('='.repeat(60));

    const formationMappings = {};
    const formationCounts = {};

    // Analyser toutes les équipes pour découvrir les patterns
    for (let i = 0; i < 20; i++) {
        const team = App.getTeam(i);
        if (!team || !team.formation || !team.formation.positions) continue;

        const formationType = team.formation.type;
        const clubPlayers = TeamManager.getClubPlayers(team.clubId);

        if (!formationMappings[formationType]) {
            formationMappings[formationType] = {};
            formationCounts[formationType] = 0;
        }

        formationCounts[formationType]++;

        console.log(`\n📋 ${team.squadName} - Formation: ${formationType}`);

        // Analyser chaque position pour déduire le rôle
        team.formation.positions.forEach((position, arrayIndex) => {
            const player = clubPlayers.find(p => p.id === position.playerId);
            if (!player || !player.metadata?.positions) return;

            const playerPositions = player.metadata.positions;
            const positionIndex = position.index !== undefined ? position.index : arrayIndex;

            // Déduire le rôle le plus probable basé sur les positions du joueur
            const probableRole = deducePlayerRole(playerPositions, positionIndex, formationType);

            if (!formationMappings[formationType][positionIndex]) {
                formationMappings[formationType][positionIndex] = {};
            }

            if (!formationMappings[formationType][positionIndex][probableRole]) {
                formationMappings[formationType][positionIndex][probableRole] = 0;
            }

            formationMappings[formationType][positionIndex][probableRole]++;

            const playerName = `${player.metadata?.firstName?.charAt(0)}.${player.metadata?.lastName}`;
            console.log(`   Index[${positionIndex}]: ${playerName} [${playerPositions.join(',')}] → Probable: ${probableRole}`);
        });
    }

    // Générer les mappings finaux
    console.log('\n🎯 MAPPINGS DÉCOUVERTS:');
    Object.keys(formationMappings).forEach(formationType => {
        console.log(`\n📐 ${formationType} (${formationCounts[formationType]} équipe(s) analysée(s)):`);

        const mapping = [];
        const maxIndex = Math.max(...Object.keys(formationMappings[formationType]).map(Number));

        for (let i = 0; i <= maxIndex; i++) {
            const positionData = formationMappings[formationType][i];
            if (positionData) {
                // Prendre le rôle le plus fréquent pour cet index
                const mostFrequentRole = Object.keys(positionData).reduce((a, b) =>
                    positionData[a] > positionData[b] ? a : b
                );
                mapping[i] = mostFrequentRole;

                const confidence = positionData[mostFrequentRole];
                const total = Object.values(positionData).reduce((sum, count) => sum + count, 0);
                const percentage = Math.round((confidence / total) * 100);

                console.log(`   Index[${i}]: ${mostFrequentRole} (${confidence}/${total} = ${percentage}%)`);
            } else {
                mapping[i] = 'CM'; // Fallback
                console.log(`   Index[${i}]: CM (fallback)`);
            }
        }

        console.log(`   📋 Mapping final: [${mapping.map(pos => `'${pos}'`).join(', ')}]`);
    });

    return formationMappings;
};

function deducePlayerRole(playerPositions, positionIndex, formationType) {
    // Logique pour déduire le rôle basé sur les positions naturelles du joueur

    // Gardien évident
    if (playerPositions.includes('GK')) return 'G';

    // Défenseurs
    if (playerPositions.includes('CB')) return 'DC';
    if (playerPositions.includes('LB') || playerPositions.includes('LWB')) return 'DLG';
    if (playerPositions.includes('RB') || playerPositions.includes('RWB')) return 'DLD';

    // Milieux défensifs
    if (playerPositions.includes('CDM')) return 'MDC';

    // Milieux latéraux
    if (playerPositions.includes('LM')) return 'MG';
    if (playerPositions.includes('RM')) return 'MD';

    // Milieux offensifs
    if (playerPositions.includes('CAM')) return 'MOC';
    if (playerPositions.includes('CM')) return 'MC';

    // Attaquants
    if (playerPositions.includes('ST')) return 'BU';
    if (playerPositions.includes('CF')) return 'AT';

    // Ailiers
    if (playerPositions.includes('LW')) return 'AG';
    if (playerPositions.includes('RW')) return 'AD';

    // Fallback basé sur la position dans la formation
    if (positionIndex === 0) return 'G';
    if (positionIndex <= 5) return 'DC'; // Défense
    if (positionIndex <= 8) return 'MC'; // Milieu
    return 'BU'; // Attaque
}

console.log('✅ Fonctions globales chargées (avec découverte de formations)');
console.log('🔧 Debug disponible via: window.debugPlayMFL');