{"name": "@motionone/svelte", "version": "10.16.4", "description": "A tiny, performant animation library for Svelte", "author": "<PERSON>", "license": "MIT", "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "types/index.d.ts", "sideEffects": false, "type": "module", "scripts": {"build": "rimraf lib dist types && tsc -p . && rollup -c", "test": "jest --coverage --config jest.config.js"}, "dependencies": {"@motionone/dom": "^10.16.4", "tslib": "^2.3.1"}, "devDependencies": {"@rollup/plugin-typescript": "^8.3.0", "@sveltejs/kit": "next", "@testing-library/svelte": "^3.0.3", "@tsconfig/svelte": "^2.0.1", "prettier-plugin-svelte": "^2.4.0", "rollup-plugin-css-only": "^3.1.0", "rollup-plugin-svelte": "^7.1.0", "svelte": "^3.42.6", "svelte-check": "^2.2.6", "svelte-jester": "^2.1.5", "svelte-preprocess": "^4.9.4"}, "gitHead": "1b73773be02af31fedb8ac4b5d391650d06f4094"}