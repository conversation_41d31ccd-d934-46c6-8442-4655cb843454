// ===============================================
// SYSTÈME DE JOUEURS ET RATINGS - PlayMFL Manager
// VERSION CORRIGÉE AVEC FIX DES INDICES DE POSITIONS
// ===============================================

const PlayerSystem = {
    
    // ===============================================
    // CALCUL DE RATING PAR POSITION
    // ===============================================
    
    calculatePositionRating(player, position) {
        const weights = POSITION_WEIGHTS[position];
        if (!weights) {
            console.warn(`Position inconnue: ${position}`);
            // Fallback sur l'overall du joueur
            return player.metadata?.overall || 50;
        }
        
        const metadata = player.metadata || {};

        // Extraire les VRAIS attributs PlayMFL (valeurs 0-99)
        const pace = metadata.pace || 0;
        const shooting = metadata.shooting || 0;
        const passing = metadata.passing || 0;
        const dribbling = metadata.dribbling || 0;
        const defense = metadata.defense || 0;
        const physical = metadata.physical || 0;
        const goalkeeping = metadata.goalkeeping || 0;
        
        // SYSTÈME OFFICIEL PLAYMFL : Pénalités de familiarité positionnelle
        const familiarity = this.getPositionFamiliarity(player, position);
        const penalty = FAMILIARITY_PENALTIES[familiarity] || 0;

        // Appliquer les pénalités PlayMFL aux attributs AVANT le calcul final
        const adjustedStats = {
            pace: Math.max(0, pace + penalty),
            shooting: Math.max(0, shooting + penalty),
            passing: Math.max(0, passing + penalty),
            dribbling: Math.max(0, dribbling + penalty),
            defense: Math.max(0, defense + penalty),
            physical: Math.max(0, physical + penalty),
            goalkeeping: Math.max(0, goalkeeping + penalty)
        };

        // Recalculer avec les stats ajustées
        const adjustedRating =
            (adjustedStats.pace * weights.PAC) +
            (adjustedStats.shooting * weights.SHO) +
            (adjustedStats.passing * weights.PAS) +
            (adjustedStats.dribbling * weights.DRI) +
            (adjustedStats.defense * weights.DEF) +
            (adjustedStats.physical * weights.PHY) +
            (adjustedStats.goalkeeping * weights.GK);

        // Bonus/malus énergie (energy va de 0 à CONFIG.MAX_ENERGY = 10000)
        const energyPercent = (player.energy || CONFIG.MAX_ENERGY) / CONFIG.MAX_ENERGY;
        const energyMultiplier = Math.max(0.7, energyPercent); // Min 70%

        // Malus suspension
        const suspensionMultiplier = this.isPlayerSuspended(player) ? 0.5 : 1.0;

        const finalRating = Math.round(adjustedRating * energyMultiplier * suspensionMultiplier);
        
        // Assurer un minimum de 10 et maximum de 99
        return Math.max(10, Math.min(99, finalRating));
    },

    // ===============================================
    // UTILITAIRES DE POSITION
    // ===============================================
    
    findSimilarPosition(targetPosition, playerPositions) {
        for (const [group, positions] of Object.entries(POSITION_GROUPS)) {
            if (positions.includes(targetPosition)) {
                return playerPositions.find(pos => positions.includes(pos));
            }
        }
        return null;
    },

    findBestPosition(player, availablePositions = null) {
        const playerPositions = player.metadata?.positions || [];
        const positionsToCheck = availablePositions || playerPositions;
        
        let bestPosition = null;
        let bestRating = 0;
        
        positionsToCheck.forEach(position => {
            const rating = this.calculatePositionRating(player, position);
            if (rating > bestRating) {
                bestRating = rating;
                bestPosition = position;
            }
        });
        
        return {
            position: bestPosition,
            rating: bestRating
        };
    },

    // ===============================================
    // SYSTÈME DE FAMILIARITÉ POSITIONNELLE PLAYMFL
    // ===============================================

    getPositionFamiliarity(player, targetPosition) {
        const playerPositions = player.metadata?.positions || [];

        // Convertir les positions API vers les codes de notre matrice
        const convertedPlayerPositions = playerPositions.map(pos => this.convertApiPositionToMatrix(pos));
        const convertedTargetPosition = this.convertApiPositionToMatrix(targetPosition);

        // Position principale (première dans la liste)
        if (convertedPlayerPositions[0] === convertedTargetPosition) {
            return 'PRIMARY';
        }

        // Position secondaire/tertiaire (dans la liste des positions)
        if (convertedPlayerPositions.includes(convertedTargetPosition)) {
            return 'SECONDARY';
        }

        // Vérifier la familiarité selon la matrice PlayMFL
        const primaryPosition = convertedPlayerPositions[0];
        if (primaryPosition && POSITION_FAMILIARITY[primaryPosition]) {
            const familiarityLevel = POSITION_FAMILIARITY[primaryPosition][convertedTargetPosition];
            if (familiarityLevel) {
                return familiarityLevel;
            }
        }

        // Position non familière par défaut
        return 'UNFAMILIAR';
    },

    convertApiPositionToMatrix(apiPosition) {
        // Mapping des positions API PlayMFL vers notre matrice de familiarité
        const positionMapping = {
            // Gardiens
            'GK': 'GK',
            'G': 'GK',

            // Défenseurs centraux
            'CB': 'CB',
            'DC': 'CB',

            // Latéraux
            'LB': 'LB',
            'DG': 'LB',
            'LWB': 'LB',
            'DLG': 'LB',

            'RB': 'RB',
            'DD': 'RB',
            'RWB': 'RB',
            'DLD': 'RB',

            // Milieux défensifs
            'CDM': 'CDM',
            'MDC': 'CDM',

            // Milieux centraux
            'CM': 'CM',
            'MC': 'CM',

            // Milieux latéraux
            'LM': 'LM',
            'MG': 'LM',

            'RM': 'RM',
            'MD': 'RM',

            // Milieux offensifs
            'CAM': 'CAM',
            'MOC': 'CAM',

            // Ailiers
            'LW': 'LW',
            'AG': 'LW',

            'RW': 'RW',
            'AD': 'RW',

            // Attaquants
            'ST': 'ST',
            'BU': 'ST',

            'CF': 'CF',
            'AT': 'CF'
        };

        return positionMapping[apiPosition] || apiPosition;
    },

    // ===============================================
    // COMPATIBILITÉ DES POSITIONS
    // ===============================================

    canPlayPosition(player, targetPosition) {
        const playerPositions = player.metadata?.positions || [];

        // 1. Vérifier si le joueur a cette position naturellement
        if (playerPositions.includes(targetPosition)) {
            return true;
        }

        // 2. Vérifier les positions similaires (ex: CAM/MOC, ST/BU, etc.)
        const similarPosition = this.findSimilarPosition(targetPosition, playerPositions);
        if (similarPosition) {
            return true;
        }

        // 3. Fallback : vérifier le rating calculé (minimum 30 pour positions non-naturelles)
        const rating = this.calculatePositionRating(player, targetPosition);
        return rating >= 30;
    },

    getPositionCompatibility(player, targetPosition) {
        const playerPositions = player.metadata?.positions || [];

        // Position naturelle = priorité maximale (100)
        if (playerPositions.includes(targetPosition)) {
            return 100;
        }

        // Position similaire = priorité élevée (80)
        const similarPosition = this.findSimilarPosition(targetPosition, playerPositions);
        if (similarPosition) {
            return 80;
        }

        // Position calculée = priorité faible (rating/2)
        const rating = this.calculatePositionRating(player, targetPosition);
        return Math.max(rating / 2, 0);
    },

    // ===============================================
    // SYSTÈME DE SUSPENSION
    // ===============================================
    
    isPlayerSuspended(player) {
        // Vérifier les suspensions dans matchesSuspensions
        if (player.matchesSuspensions && player.matchesSuspensions.length > 0) {
            // Un joueur est suspendu s'il a des matchs de suspension avec status "PLANNED"
            return player.matchesSuspensions.some(suspension => {
                return suspension.match && suspension.match.status === "PLANNED";
            });
        }
        
        return false;
    },

    // ===============================================
    // FONCTIONS DE DEBUG
    // ===============================================

    debugPlayerForPosition(player, targetPosition) {
        const playerPositions = player.metadata?.positions || [];
        const familiarity = this.getPositionFamiliarity(player, targetPosition);
        const penalty = FAMILIARITY_PENALTIES[familiarity] || 0;
        const compatibility = this.getPositionCompatibility(player, targetPosition);
        const rating = this.calculatePositionRating(player, targetPosition);
        const canPlay = this.canPlayPosition(player, targetPosition);

        console.log(`🔍 ${player.metadata?.firstName} ${player.metadata?.lastName} pour ${targetPosition}:`);
        console.log(`   📋 Positions naturelles: [${playerPositions.join(', ')}]`);
        console.log(`   🎯 Familiarité PlayMFL: ${familiarity} (${penalty >= 0 ? '+' : ''}${penalty} aux attributs)`);
        console.log(`   ✅ Peut jouer: ${canPlay ? 'OUI' : 'NON'}`);
        console.log(`   🎯 Compatibilité: ${compatibility}/100`);
        console.log(`   ⭐ Rating final: ${rating}`);
        console.log(`   ⚡ Énergie: ${player.energy}/10000`);

        return { familiarity, penalty, compatibility, rating, canPlay };
    },

    // ===============================================
    // RECHERCHE DE REMPLAÇANTS AVANCÉE
    // ===============================================
    
    findReplacementAdvanced(currentPlayer, allPlayers, targetPosition, minEnergy = 0, currentPositions = [], avoidCards = false) {
        const currentPositionIds = currentPositions.map(p => p.playerId);

        const candidates = allPlayers.filter(player => {
            if (player.id === currentPlayer.id) return false;
            if (currentPositionIds.includes(player.id)) return false;
            if (player.energy < minEnergy) return false;
            if (avoidCards && this.isPlayerSuspended(player)) return false;

            // AMÉLIORATION : Vérifier que le joueur peut naturellement jouer à cette position
            return this.canPlayPosition(player, targetPosition);
        });

        // Trier par compatibilité et rating pour cette position
        candidates.sort((a, b) => {
            const compatibilityA = this.getPositionCompatibility(a, targetPosition);
            const compatibilityB = this.getPositionCompatibility(b, targetPosition);
            const ratingA = this.calculatePositionRating(a, targetPosition);
            const ratingB = this.calculatePositionRating(b, targetPosition);

            // Prioriser d'abord la compatibilité (position naturelle vs secondaire)
            if (compatibilityB !== compatibilityA) return compatibilityB - compatibilityA;

            // Puis le rating pour cette position
            if (ratingB !== ratingA) return ratingB - ratingA;

            // En cas d'égalité, prioriser l'énergie puis l'overall
            if (b.energy !== a.energy) return b.energy - a.energy;
            return (b.metadata?.overall || 0) - (a.metadata?.overall || 0);
        });

        return candidates[0] || null;
    },

    // ===============================================
    // OPTIMISATION DE FORMATION - CORRIGÉE
    // ===============================================
    
    optimizeFormation(team, clubPlayers) {
        if (!team.formation || !team.formation.positions) return null;
        
        const formationType = team.formation.type;
        const expectedPositions = FORMATION_TO_POSITIONS[formationType] || 
                                 Array(11).fill('').map((_, i) => i === 0 ? 'GK' : 'CM');
        
        const optimizedPositions = [];
        const usedPlayers = new Set();
        
        // 1. Assigner le gardien en premier (position index 0)
        const goalkeepers = clubPlayers.filter(p => {
            const positions = p.metadata?.positions || [];
            return (positions.includes('GK') || positions.includes('G')) &&
                   !this.isPlayerSuspended(p) &&
                   p.energy > 5000; // Au moins 50% d'énergie
        }).sort((a, b) => this.calculatePositionRating(b, 'GK') - this.calculatePositionRating(a, 'GK'));
        
        if (goalkeepers.length > 0) {
            optimizedPositions[0] = {
                index: 0,
                playerId: goalkeepers[0].id
            };
            usedPlayers.add(goalkeepers[0].id);
        }
        
        // 2. Assigner les autres positions par ordre d'importance
        for (let posIndex = 1; posIndex < expectedPositions.length; posIndex++) {
            const targetPosition = expectedPositions[posIndex];
            
            const availablePlayers = clubPlayers.filter(p => 
                !usedPlayers.has(p.id) && 
                !this.isPlayerSuspended(p) &&
                p.energy > 3000 && // Au moins 30% d'énergie
                this.calculatePositionRating(p, targetPosition) >= 20
            );
            
            if (availablePlayers.length > 0) {
                // Trier par rating pour cette position
                availablePlayers.sort((a, b) => 
                    this.calculatePositionRating(b, targetPosition) - this.calculatePositionRating(a, targetPosition)
                );
                
                // CORRECTION : S'assurer que l'index est correct
                optimizedPositions[posIndex] = {
                    index: posIndex,
                    playerId: availablePlayers[0].id
                };
                usedPlayers.add(availablePlayers[0].id);
            }
        }
        
        return optimizedPositions.filter(pos => pos !== undefined);
    },

    // ===============================================
    // ANALYSE D'ÉQUIPE - CORRIGÉE
    // ===============================================
    
    analyzeTeamStrength(team, clubPlayers) {
        if (!team.formation || !team.formation.positions) return null;
        
        const formationType = team.formation.type;
        const expectedPositions = FORMATION_TO_POSITIONS[formationType] || [];
        
        let totalRating = 0;
        let positionCount = 0;
        let weakPositions = [];
        let strongPositions = [];
        let suspendedPlayers = [];
        let lowEnergyPlayers = [];
        
        team.formation.positions.forEach((position, arrayIndex) => {
            const player = clubPlayers.find(p => p.id === position.playerId);
            
            // CORRECTION : Utiliser l'index de la position
            const positionIndex = position.index !== undefined ? position.index : arrayIndex;
            const expectedPosition = expectedPositions[positionIndex] || 'CM';
            
            if (player) {
                const rating = this.calculatePositionRating(player, expectedPosition);
                totalRating += rating;
                positionCount++;
                
                // Analyser les problèmes
                if (this.isPlayerSuspended(player)) {
                    suspendedPlayers.push({ player, position: expectedPosition, index: positionIndex });
                }
                
                if (player.energy < 5000) { // Moins de 50%
                    lowEnergyPlayers.push({ player, position: expectedPosition, index: positionIndex, energy: player.energy });
                }
                
                // Classer les positions
                if (rating < 50) {
                    weakPositions.push({
                        position: expectedPosition,
                        player: player,
                        rating: rating,
                        index: positionIndex
                    });
                } else if (rating > 75) {
                    strongPositions.push({
                        position: expectedPosition,
                        player: player,
                        rating: rating,
                        index: positionIndex
                    });
                }
            }
        });
        
        return {
            averageRating: positionCount > 0 ? Math.round(totalRating / positionCount) : 0,
            weakPositions: weakPositions.sort((a, b) => a.rating - b.rating),
            strongPositions: strongPositions.sort((a, b) => b.rating - a.rating),
            suspendedPlayers: suspendedPlayers,
            lowEnergyPlayers: lowEnergyPlayers.sort((a, b) => a.energy - b.energy),
            totalPlayers: positionCount
        };
    },

    // ===============================================
    // SUGGESTIONS D'AMÉLIORATION - CORRIGÉE
    // ===============================================
    
    suggestImprovements(team, clubPlayers) {
        const analysis = this.analyzeTeamStrength(team, clubPlayers);
        if (!analysis) return [];
        
        const suggestions = [];
        
        // Suggestions pour joueurs suspendus (priorité max)
        analysis.suspendedPlayers.forEach(suspended => {
            const betterPlayers = clubPlayers.filter(p => {
                const currentIds = team.formation.positions.map(pos => pos.playerId);
                return !currentIds.includes(p.id) && 
                       !this.isPlayerSuspended(p) &&
                       this.calculatePositionRating(p, suspended.position) >= 20;
            }).sort((a, b) => 
                this.calculatePositionRating(b, suspended.position) - this.calculatePositionRating(a, suspended.position)
            );
            
            if (betterPlayers.length > 0) {
                suggestions.push({
                    type: 'suspension',
                    position: suspended.position,
                    current: suspended.player,
                    suggested: betterPlayers[0],
                    priority: 10,
                    message: `🚨 URGENT: ${suspended.player.metadata?.firstName} ${suspended.player.metadata?.lastName} est suspendu en ${suspended.position}`
                });
            }
        });
        
        // Suggestions pour joueurs fatigués
        analysis.lowEnergyPlayers.forEach(tired => {
            const betterPlayers = clubPlayers.filter(p => {
                const currentIds = team.formation.positions.map(pos => pos.playerId);
                return !currentIds.includes(p.id) && 
                       !this.isPlayerSuspended(p) &&
                       p.energy > tired.energy + 2000 &&
                       this.calculatePositionRating(p, tired.position) >= 30;
            }).sort((a, b) => 
                this.calculatePositionRating(b, tired.position) - this.calculatePositionRating(a, tired.position)
            );
            
            if (betterPlayers.length > 0) {
                suggestions.push({
                    type: 'fatigue',
                    position: tired.position,
                    current: tired.player,
                    suggested: betterPlayers[0],
                    priority: 8,
                    message: `⚡ ${tired.player.metadata?.firstName} est fatigué (${Math.round(tired.energy/100)}%) en ${tired.position}`
                });
            }
        });
        
        // Suggestions pour positions faibles
        analysis.weakPositions.forEach(weak => {
            const betterPlayers = clubPlayers.filter(p => {
                const currentIds = team.formation.positions.map(pos => pos.playerId);
                return !currentIds.includes(p.id) && 
                       !this.isPlayerSuspended(p) &&
                       p.energy > 5000 &&
                       this.calculatePositionRating(p, weak.position) > weak.rating + 10;
            }).sort((a, b) => 
                this.calculatePositionRating(b, weak.position) - this.calculatePositionRating(a, weak.position)
            );
            
            if (betterPlayers.length > 0) {
                const improvement = this.calculatePositionRating(betterPlayers[0], weak.position) - weak.rating;
                suggestions.push({
                    type: 'improvement',
                    position: weak.position,
                    current: weak.player,
                    suggested: betterPlayers[0],
                    improvement: improvement,
                    priority: Math.min(7, improvement / 5),
                    message: `📈 Améliorer ${weak.position}: ${weak.player.metadata?.firstName} (${weak.rating}) → ${betterPlayers[0].metadata?.firstName} (+${improvement})`
                });
            }
        });
        
        return suggestions.sort((a, b) => b.priority - a.priority).slice(0, 5);
    },

    // ===============================================
    // STATISTIQUES D'ÉQUIPE - CORRIGÉE
    // ===============================================
    
    calculateTeamStats(team, clubPlayers) {
        const formation = team.formation;
        
        let stats = {
            avgEnergy: 0,
            suspensions: 0,
            playerCount: 0,
            avgRating: 0,
            totalRating: 0,
            lowEnergyPlayers: 0
        };
        
        if (formation && formation.positions && clubPlayers.length > 0) {
            let totalEnergy = 0;
            let totalRating = 0;
            let validPlayers = 0;
            
            const formationType = formation.type;
            const expectedPositions = FORMATION_TO_POSITIONS[formationType] || [];
            
            formation.positions.forEach((position, arrayIndex) => {
                const player = clubPlayers.find(p => p.id === position.playerId);
                if (player) {
                    totalEnergy += player.energy || 0;
                    
                    // Compter les suspensions actives
                    if (this.isPlayerSuspended(player)) {
                        stats.suspensions++;
                    }
                    
                    // Compter les joueurs fatigués
                    if (player.energy < 5000) {
                        stats.lowEnergyPlayers++;
                    }
                    
                    // CORRECTION : Calculer le rating avec le bon index de position
                    const positionIndex = position.index !== undefined ? position.index : arrayIndex;
                    const expectedPosition = expectedPositions[positionIndex] || 'CM';
                    const rating = this.calculatePositionRating(player, expectedPosition);
                    totalRating += rating;
                    
                    validPlayers++;
                }
            });
            
            stats.avgEnergy = validPlayers > 0 ? Math.round(totalEnergy / validPlayers / 100) : 0;
            stats.avgRating = validPlayers > 0 ? Math.round(totalRating / validPlayers) : 0;
            stats.totalRating = totalRating;
            stats.playerCount = validPlayers;
        }
        
        return stats;
    },

    // ===============================================
    // UTILITAIRES VISUELS
    // ===============================================
    
    getPlayerRatingClass(rating) {
        if (rating >= 80) return 'rating-excellent';
        if (rating >= 65) return 'rating-good';
        if (rating >= 45) return 'rating-average';
        return 'rating-poor';
    },

    displayPlayerRating(player, position) {
        const rating = this.calculatePositionRating(player, position);
        const ratingClass = this.getPlayerRatingClass(rating);
        return `<span class="position-rating ${ratingClass}">${rating}</span>`;
    },

    // ===============================================
    // UTILITAIRES DE DEBUG
    // ===============================================
    
    debugPlayer(player, position) {
        const metadata = player.metadata || {};
        console.log(`🔍 Debug joueur: ${metadata.firstName} ${metadata.lastName}`);
        console.log(`📊 Attributs:`, {
            pace: metadata.pace,
            shooting: metadata.shooting,
            passing: metadata.passing,
            dribbling: metadata.dribbling,
            defense: metadata.defense,
            physical: metadata.physical,
            goalkeeping: metadata.goalkeeping,
            overall: metadata.overall
        });
        console.log(`🎯 Position testée: ${position}`);
        console.log(`📈 Rating calculé: ${this.calculatePositionRating(player, position)}`);
        console.log(`⚡ Énergie: ${player.energy}/10000 (${Math.round(player.energy/100)}%)`);
        console.log(`🟥 Suspendu: ${this.isPlayerSuspended(player)}`);
        if (player.nbSeasonYellows) {
            console.log(`📝 Cartons jaunes saison: ${player.nbSeasonYellows}`);
        }
    }
};

console.log('✅ Système de joueurs et ratings chargé (version PlayMFL corrigée)');