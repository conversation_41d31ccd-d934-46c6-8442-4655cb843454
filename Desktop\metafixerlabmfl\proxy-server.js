const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3000;

// CORS en premier
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'Referer'],
    credentials: false
}));

// Logging simple
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    next();
});

// ===============================================
// SERVIR LES FICHIERS STATIQUES (VOTRE FRONT)
// ===============================================
app.use(express.static(path.join(__dirname)));

// ===============================================
// ROUTES DE TEST
// ===============================================


app.get('/test', (req, res) => {
    res.json({
        message: 'Serveur proxy PlayMFL fonctionnel !',
        timestamp: new Date().toISOString(),
        version: 'simplifiée'
    });
});

// ===============================================
// PROXY API PLAYMFL - VOTRE CONFIGURATION EXISTANTE
// ===============================================
app.use('/api', createProxyMiddleware({
    target: 'https://z519wdyajg.execute-api.us-east-1.amazonaws.com/prod',
    changeOrigin: true,
    secure: true,
    timeout: 60000,
    pathRewrite: {
        '^/api': '',
    },
    onProxyReq: (proxyReq, req, res) => {
        const finalUrl = `https://z519wdyajg.execute-api.us-east-1.amazonaws.com/prod${req.url.replace('/api', '')}`;
        console.log(`🚀 PROXY: ${req.method} ${finalUrl}`);
        
        // Headers minimaux mais suffisants
        proxyReq.setHeader('Accept', '*/*');
        proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
        proxyReq.setHeader('Accept-Language', 'fr-FR,fr;q=0.9,en-US;q=0.8,en;q=0.7');
        proxyReq.setHeader('Origin', 'https://app.playmfl.com');
        proxyReq.setHeader('Referer', 'https://app.playmfl.com/');
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        proxyReq.setHeader('Sec-Fetch-Dest', 'empty');
        proxyReq.setHeader('Sec-Fetch-Mode', 'cors');
        proxyReq.setHeader('Sec-Fetch-Site', 'cross-site');
        
        // Auth header
        if (req.headers.authorization) {
            proxyReq.setHeader('Authorization', req.headers.authorization);
            console.log(`🔑 Auth: Présent`);
        } else {
            console.log(`❌ Auth: MANQUANT`);
        }
        
        // Content-Type pour les requêtes avec body
        if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
            proxyReq.setHeader('Content-Type', 'application/json');
            console.log(`📝 ${req.method} - Content-Type: application/json`);
        }
        
        console.log(`📋 Headers: Auth=${!!req.headers.authorization}, Method=${req.method}`);
    },
    onProxyRes: (proxyRes, req, res) => {
        const finalUrl = `https://z519wdyajg.execute-api.us-east-1.amazonaws.com/prod${req.url.replace('/api', '')}`;
        const emoji = proxyRes.statusCode >= 400 ? '❌' : '✅';
        console.log(`${emoji} RÉPONSE: ${req.method} ${finalUrl} → ${proxyRes.statusCode} ${proxyRes.statusMessage}`);
        
        // CORS headers
        proxyRes.headers['Access-Control-Allow-Origin'] = '*';
        proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS, PATCH';
        proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Accept, Origin, Referer';
        
        // Log des erreurs
        if (proxyRes.statusCode >= 400) {
            console.log(`📋 Headers d'erreur:`, Object.keys(proxyRes.headers));
            
            // Lire le body d'erreur
            let errorBody = '';
            proxyRes.on('data', (chunk) => {
                errorBody += chunk;
            });
            proxyRes.on('end', () => {
                if (errorBody) {
                    console.log(`📄 Body d'erreur:`, errorBody.substring(0, 500));
                }
            });
        }
    },
    onError: (err, req, res) => {
        const finalUrl = `https://z519wdyajg.execute-api.us-east-1.amazonaws.com/prod${req.url.replace('/api', '')}`;
        console.error(`❌ ERREUR PROXY: ${req.method} ${finalUrl}`);
        console.error(`   Code: ${err.code}`);
        console.error(`   Message: ${err.message}`);
        
        // Réponse d'erreur si pas déjà envoyée
        if (!res.headersSent) {
            res.status(500).json({ 
                error: 'Erreur du serveur proxy',
                message: err.message,
                code: err.code,
                url: finalUrl
            });
        }
    }
}));

// ===============================================
// GESTION OPTIONS
// ===============================================
app.options('*', (req, res) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, Origin, Referer');
    res.sendStatus(200);
});

// ===============================================
// GESTION D'ERREURS GLOBALE
// ===============================================
app.use((err, req, res, next) => {
    console.error('❌ Erreur serveur:', err.message);
    if (!res.headersSent) {
        res.status(500).json({ 
            error: 'Erreur interne du serveur',
            message: err.message 
        });
    }
});

// ===============================================
// DÉMARRAGE DU SERVEUR
// ===============================================
app.listen(PORT, () => {
    console.log('🚀 ========================================');
    console.log('🎯 Serveur Proxy PlayMFL démarré !');
    console.log('🌐 URL: http://localhost:' + PORT);
    console.log('🔗 API Proxy: http://localhost:' + PORT + '/api');
    console.log('🧪 Test: http://localhost:' + PORT + '/test');
    console.log('📁 Fichiers statiques: ' + __dirname);
    console.log('✅ Prêt pour PlayMFL Manager !');
    console.log('========================================');
});

// ===============================================
// GESTION PROPRE DE L'ARRÊT
// ===============================================
process.on('SIGINT', () => {
    console.log('\n👋 Arrêt du serveur proxy...');
    process.exit(0);
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (err) => {
    console.error('💥 Exception non gérée:', err.message);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Promesse rejetée:', reason);
});