{"name": "playmfl-dapper-auth", "version": "1.0.0", "description": "API Node.js pour l'authentification Dapper Wallet avec PlayMFL", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["dapper", "flow", "blockchain", "playmfl", "authentication"], "author": "Votre nom", "license": "MIT", "dependencies": {"@onflow/fcl": "^1.12.2", "cors": "^2.8.5", "express": "^4.18.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}