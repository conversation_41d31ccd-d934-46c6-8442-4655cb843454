const t=t=>t,n=(t,n,e)=>(((1-3*e+3*n)*t+(3*e-6*n))*t+3*n)*t,e=1e-7,a=12;function r(r,o,h,c){if(r===o&&h===c)return t;const i=t=>function(t,r,o,h,c){let i,M,u=0;do{M=r+(o-r)/2,i=n(M,h,c)-t,i>0?o=M:r=M}while(Math.abs(i)>e&&++u<a);return M}(t,0,1,r,h);return t=>0===t||1===t?t:n(i(t),o,c)}const o=(t,n="end")=>e=>{const a=(e="end"===n?Math.min(e,.999):Math.max(e,.001))*t,r="end"===n?Math.floor(a):Math.ceil(a);return o=0,h=1,c=r/t,Math.min(Math.max(c,o),h);var o,h,c};export{r as cubicBezier,o as steps};
