{"version": 3, "file": "custom-element.js", "sources": ["../../src/decorators/custom-element.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n * SPDX-License-Identifier: BSD-3-Clause\n */\n\n/*\n * IMPORTANT: For compatibility with <PERSON><PERSON><PERSON> and the Closure JS compiler, all\n * property decorators (but not class decorators) in this file that have\n * an @ExportDecoratedItems annotation must be defined as a regular function,\n * not an arrow function.\n */\nimport {Constructor, ClassDescriptor} from './base.js';\n\n/**\n * Allow for custom element classes with private constructors\n */\ntype CustomElementClass = Omit<typeof HTMLElement, 'new'>;\n\nconst legacyCustomElement = (tagName: string, clazz: CustomElementClass) => {\n  customElements.define(tagName, clazz as CustomElementConstructor);\n  // Cast as any because TS doesn't recognize the return type as being a\n  // subtype of the decorated class when clazz is typed as\n  // `Constructor<HTMLElement>` for some reason.\n  // `Constructor<HTMLElement>` is helpful to make sure the decorator is\n  // applied to elements however.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return clazz as any;\n};\n\nconst standardCustomElement = (\n  tagName: string,\n  descriptor: ClassDescriptor\n) => {\n  const {kind, elements} = descriptor;\n  return {\n    kind,\n    elements,\n    // This callback is called once the class is otherwise fully defined\n    finisher(clazz: Constructor<HTMLElement>) {\n      customElements.define(tagName, clazz);\n    },\n  };\n};\n\n/**\n * Class decorator factory that defines the decorated class as a custom element.\n *\n * ```js\n * @customElement('my-element')\n * class MyElement extends LitElement {\n *   render() {\n *     return html``;\n *   }\n * }\n * ```\n * @category Decorator\n * @param tagName The tag name of the custom element to define.\n */\nexport const customElement =\n  (tagName: string) =>\n  (classOrDescriptor: CustomElementClass | ClassDescriptor) =>\n    typeof classOrDescriptor === 'function'\n      ? legacyCustomElement(tagName, classOrDescriptor)\n      : standardCustomElement(tagName, classOrDescriptor as ClassDescriptor);\n"], "names": ["customElement", "tagName", "classOrDescriptor", "clazz", "customElements", "define", "legacyCustomElement", "descriptor", "kind", "elements", "finisher", "standardCustomElement"], "mappings": ";;;;;AAmBA,MAwCaA,EACVC,GACAC,GAC8B,mBAAtBA,EA3CiB,EAACD,EAAiBE,KAC5CC,eAAeC,OAAOJ,EAASE,GAOxBA,GAoCDG,CAAoBL,EAASC,GAjCP,EAC5BD,EACAM,KAEA,MAAMC,KAACA,EAAIC,SAAEA,GAAYF,EACzB,MAAO,CACLC,OACAC,WAEAC,SAASP,GACPC,eAAeC,OAAOJ,EAASE,EAChC,EACF,EAsBKQ,CAAsBV,EAASC"}