const t=t=>1e3*t,e=t=>t/1e3;function r(t,e,r){const a=Math.max(e-5,0);return n=r-t(a),(s=e-a)?n*(1e3/s):0;var n,s}const a=100,n=10,s=1;const c=({stiffness:t=a,damping:c=n,mass:o=s,from:h=0,to:d=1,velocity:i=0,restSpeed:u,restDistance:M}={})=>{i=i?e(i):0;const g={done:!1,hasReachedTarget:!1,current:h,target:d},v=d-h,f=Math.sqrt(t/o)/1e3,p=((t=a,e=n,r=s)=>e/(2*Math.sqrt(t*r)))(t,c,o),m=Math.abs(v)<5;let b;if(u||(u=m?.01:2),M||(M=m?.005:.5),p<1){const t=f*Math.sqrt(1-p*p);b=e=>d-Math.exp(-p*f*e)*((p*f*v-i)/t*Math.sin(t*e)+v*Math.cos(t*e))}else b=t=>d-Math.exp(-f*t)*(v+(f*v-i)*t);return t=>{g.current=b(t);const e=0===t?i:r(b,t,g.current),a=Math.abs(e)<=u,n=Math.abs(d-g.current)<=M;var s,c,o;return g.done=a&&n,g.hasReachedTarget=(s=h,c=d,o=g.current,s<c&&o>=c||s>c&&o<=c),g}},o=({from:e=0,velocity:a=0,power:n=.8,decay:s=.325,bounceDamping:o,bounceStiffness:h,changeTarget:d,min:i,max:u,restDistance:M=.5,restSpeed:g})=>{s=t(s);const v={hasReachedTarget:!1,done:!1,current:e,target:e},f=t=>void 0===i?u:void 0===u||Math.abs(i-t)<Math.abs(u-t)?i:u;let p=n*a;const m=e+p,b=void 0===d?m:d(m);v.target=b,b!==m&&(p=b-e);const l=t=>-p*Math.exp(-t/s),x=t=>b+l(t),T=t=>{const e=l(t),r=x(t);v.done=Math.abs(e)<=M,v.current=v.done?b:r};let R,y;const D=t=>{var e;(e=v.current,void 0!==i&&e<i||void 0!==u&&e>u)&&(R=t,y=c({from:v.current,to:f(v.current),velocity:r(x,t,v.current),damping:o,stiffness:h,restDistance:M,restSpeed:g}))};return D(0),t=>{let e=!1;return y||void 0!==R||(e=!0,T(t),D(t)),void 0!==R&&t>R?(v.hasReachedTarget=!0,y(t-R)):(v.hasReachedTarget=!1,!e&&T(t),v)}};export{o as glide};
