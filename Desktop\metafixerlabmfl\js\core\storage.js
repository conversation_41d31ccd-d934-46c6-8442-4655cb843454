// ===============================================
// GESTIONNAIRE DE STOCKAGE - PlayMFL Manager
// Version simplifiée - Connexion manuelle uniquement
// ===============================================

const StorageManager = {
    
    // Clés de stockage
    KEYS: {
        TOKEN: 'playmfl_token',
        SETTINGS: 'playmfl_settings',
        CACHE: 'playmfl_cache'
    },
    
    // ===============================================
    // GESTION DU TOKEN
    // ===============================================
    
    saveToken(token) {
        try {
            localStorage.setItem(this.KEYS.TOKEN, token);
            console.log('💾 Token sauvegardé');
        } catch (error) {
            console.warn('⚠️ Impossible de sauvegarder le token:', error);
        }
    },

    loadSavedToken() {
        try {
            const savedToken = localStorage.getItem(this.KEYS.TOKEN);
            if (savedToken) {
                console.log('✅ Token rechargé depuis le stockage');
                return savedToken;
            }
        } catch (error) {
            console.warn('⚠️ Impossible de charger le token sauvé:', error);
        }
        return null;
    },

    clearToken() {
        try {
            localStorage.removeItem(this.KEYS.TOKEN);
            console.log('🗑️ Token supprimé');
        } catch (error) {
            console.warn('⚠️ Impossible de supprimer le token:', error);
        }
    },

    // ===============================================
    // GESTION DES PARAMÈTRES
    // ===============================================
    
    saveSettings(settings) {
        try {
            localStorage.setItem(this.KEYS.SETTINGS, JSON.stringify(settings));
            console.log('💾 Paramètres sauvegardés');
        } catch (error) {
            console.warn('⚠️ Impossible de sauvegarder les paramètres:', error);
        }
    },

    loadSettings() {
        try {
            const savedSettings = localStorage.getItem(this.KEYS.SETTINGS);
            if (savedSettings) {
                return JSON.parse(savedSettings);
            }
        } catch (error) {
            console.warn('⚠️ Impossible de charger les paramètres:', error);
        }
        
        // Paramètres par défaut
        return {
            fatigueThreshold: CONFIG.DEFAULT_FATIGUE_THRESHOLD,
            theme: 'dark',
            language: 'fr',
            autoSave: false,
            notifications: true
        };
    },

    // ===============================================
    // GESTION DES DONNÉES GÉNÉRIQUES
    // ===============================================
    
    // Sauvegarder une donnée dans le stockage local
    saveData(key, value) {
        try {
            localStorage.setItem(`playmfl_${key}`, JSON.stringify(value));
            console.log(`💾 Donnée "${key}" sauvegardée`);
        } catch (error) {
            console.warn(`⚠️ Impossible de sauvegarder "${key}":`, error);
        }
    },

    // Charger une donnée depuis le stockage local
    loadData(key) {
        try {
            const savedData = localStorage.getItem(`playmfl_${key}`);
            if (savedData) {
                return JSON.parse(savedData);
            }
        } catch (error) {
            console.warn(`⚠️ Impossible de charger "${key}":`, error);
        }
        return null;
    },

    // Supprimer une donnée du stockage local
    removeData(key) {
        try {
            localStorage.removeItem(`playmfl_${key}`);
            console.log(`🗑️ Donnée "${key}" supprimée`);
        } catch (error) {
            console.warn(`⚠️ Impossible de supprimer "${key}":`, error);
        }
    },

    // ===============================================
    // CACHE DES DONNÉES
    // ===============================================
    
    saveToCache(key, data, ttl = 3600000) { // TTL par défaut: 1 heure
        try {
            const cacheData = {
                data: data,
                timestamp: Date.now(),
                ttl: ttl
            };
            
            const cache = this.getCache();
            cache[key] = cacheData;
            
            localStorage.setItem(this.KEYS.CACHE, JSON.stringify(cache));
            console.log(`💾 Données mises en cache: ${key}`);
        } catch (error) {
            console.warn('⚠️ Impossible de sauvegarder en cache:', error);
        }
    },

    loadFromCache(key) {
        try {
            const cache = this.getCache();
            const cacheEntry = cache[key];
            
            if (cacheEntry) {
                const now = Date.now();
                const age = now - cacheEntry.timestamp;
                
                if (age < cacheEntry.ttl) {
                    console.log(`✅ Données chargées depuis le cache: ${key}`);
                    return cacheEntry.data;
                } else {
                    // Cache expiré, le supprimer
                    delete cache[key];
                    localStorage.setItem(this.KEYS.CACHE, JSON.stringify(cache));
                    console.log(`🗑️ Cache expiré supprimé: ${key}`);
                }
            }
        } catch (error) {
            console.warn('⚠️ Impossible de charger depuis le cache:', error);
        }
        return null;
    },

    getCache() {
        try {
            const cache = localStorage.getItem(this.KEYS.CACHE);
            return cache ? JSON.parse(cache) : {};
        } catch (error) {
            console.warn('⚠️ Cache corrompu, réinitialisation');
            return {};
        }
    },

    clearCache() {
        try {
            localStorage.removeItem(this.KEYS.CACHE);
            console.log('🗑️ Cache vidé');
        } catch (error) {
            console.warn('⚠️ Impossible de vider le cache:', error);
        }
    },



    // ===============================================
    // GESTION GLOBALE
    // ===============================================
    
    clearAll() {
        try {
            Object.values(this.KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            
            // Supprimer aussi les données custom avec préfixe
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.startsWith('playmfl_')) {
                    localStorage.removeItem(key);
                }
            }
            
            console.log('🗑️ Toutes les données supprimées');
        } catch (error) {
            console.warn('⚠️ Impossible de supprimer toutes les données:', error);
        }
    },

    getStorageInfo() {
        try {
            const info = {
                hasToken: !!localStorage.getItem(this.KEYS.TOKEN),
                hasSettings: !!localStorage.getItem(this.KEYS.SETTINGS),
                hasCache: !!localStorage.getItem(this.KEYS.CACHE),
                storageUsed: 0
            };
            
            // Calculer l'espace utilisé (approximatif)
            let totalSize = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    totalSize += localStorage[key].length;
                }
            }
            info.storageUsed = Math.round(totalSize / 1024); // en KB
            
            return info;
        } catch (error) {
            console.warn('⚠️ Impossible d\'obtenir les infos de stockage:', error);
            return null;
        }
    },

    // ===============================================
    // EXPORT/IMPORT
    // ===============================================
    
    exportData() {
        try {
            const data = {
                settings: this.loadSettings(),
                timestamp: Date.now(),
                version: '2.0'
            };
            
            return JSON.stringify(data, null, 2);
        } catch (error) {
            console.error('❌ Erreur lors de l\'export:', error);
            return null;
        }
    },

    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (data.version && data.settings) {
                this.saveSettings(data.settings);
                console.log('✅ Données importées avec succès');
                return true;
            } else {
                throw new Error('Format de données invalide');
            }
        } catch (error) {
            console.error('❌ Erreur lors de l\'import:', error);
            return false;
        }
    }
};

console.log('✅ Gestionnaire de stockage chargé (version simplifiée)');