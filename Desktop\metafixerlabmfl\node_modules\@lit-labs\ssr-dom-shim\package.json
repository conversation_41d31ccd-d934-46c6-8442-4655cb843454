{"name": "@lit-labs/ssr-dom-shim", "version": "1.3.0", "publishConfig": {"access": "public"}, "description": "DOM shim for Lit Server Side Rendering (SSR)", "license": "BSD-3-<PERSON><PERSON>", "author": "Google LLC", "homepage": "https://github.com/lit/lit/tree/main/packages/labs/ssr-dom-shim", "repository": {"type": "git", "url": "https://github.com/lit/lit.git", "directory": "packages/labs/ssr-dom-shim"}, "main": "index.js", "typings": "index.d.ts", "type": "module", "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}}, "files": ["index.{d.ts,d.ts.map,js,js.map}", "lib/"], "scripts": {"build": "wireit", "build:ts": "wireit", "test": "wireit"}, "wireit": {"build": {"dependencies": ["build:ts"]}, "build:ts": {"command": "tsc --build --pretty", "clean": "if-file-deleted", "files": ["src/**/*.ts", "tsconfig.json"], "output": ["lib/", "index.{d.ts,d.ts.map,js,js.map}", "tsconfig.tsbuildinfo"]}, "test": {"command": "uvu test \"_test\\.js$\"", "dependencies": ["build"], "env": {"NODE_OPTIONS": "--enable-source-maps"}, "output": []}}}